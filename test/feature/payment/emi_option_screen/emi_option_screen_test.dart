import 'package:evoapp/data/response/emi_package_entity.dart';
import 'package:evoapp/data/response/emi_tenor_offer_entity.dart';
import 'package:evoapp/data/response/order_session_entity.dart';
import 'package:evoapp/data/response/store_info_entity.dart';
import 'package:evoapp/data/response/voucher_entity.dart';
import 'package:evoapp/feature/feature_toggle.dart';
import 'package:evoapp/feature/payment/base_page_payment/cubit/update_order_cubit.dart';
import 'package:evoapp/feature/payment/emi_option_screen/bloc/emi_option_screen_cubit.dart';
import 'package:evoapp/feature/payment/emi_option_screen/bloc/emi_option_screen_state.dart';
import 'package:evoapp/feature/payment/emi_option_screen/emi_option_screen.dart';
import 'package:evoapp/feature/payment/models/emi_option_cache_data_model.dart';
import 'package:evoapp/feature/payment/models/emi_option_ui_model.dart';
import 'package:evoapp/feature/payment/utils/payment_with_emi_utils.dart';
import 'package:evoapp/feature/payment/widget/emi_tenor_list_widget/emi_tenor_list_widget.dart';
import 'package:evoapp/feature/payment/widget/payment_detail_info/emi_summary_info_widget.dart';
import 'package:evoapp/feature/payment/widget/payment_title_widget/emi_payment_title_widget.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/global.dart';
import 'package:evoapp/resources/ui_strings.dart';
import 'package:evoapp/util/evo_authentication_helper.dart';
import 'package:evoapp/util/evo_snackbar.dart';
import 'package:evoapp/util/ui_utils/evo_dialog_helper.dart';
import 'package:evoapp/util/ui_utils/evo_ui_utils.dart';
import 'package:evoapp/widget/cta_with_powered_by_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_common_package/util/common_navigator.dart';
import 'package:flutter_common_package/util/functions.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../base/evo_page_state_base_test_config.dart';
import '../../../util/flutter_test_config.dart';

// Create mock classes
class MockEmiOptionScreenCubit extends Mock implements EmiOptionScreenCubit {}

class MockUpdateOrderCubit extends Mock implements UpdateOrderCubit {}

class MockPaymentWithEMIUtils extends Mock implements PaymentWithEMIUtils {}

class MockEvoUiUtils extends Mock implements EvoUiUtils {}

class MockEvoDialogHelper extends Mock implements EvoDialogHelper {}

class MockBuildContext extends Mock implements BuildContext {}

void main() {
  late MockEmiOptionScreenCubit mockEmiOptionScreenCubit;
  late MockUpdateOrderCubit mockUpdateOrderCubit;
  late MockPaymentWithEMIUtils mockPaymentWithEMIUtils;

  // Test data
  final OrderSessionEntity orderSessionEntity = OrderSessionEntity(
    id: 'test_order_id',
    storeInfo: StoreInfoEntity(merchantName: 'Test Merchant'),
    orderAmount: 1000000,
  );

  final EmiPackageEntity firstEmiPackage = EmiPackageEntity(
    offer: EmiTenorOfferEntity(id: '1', tenor: 3, interestRate: 0.0, isRecommended: true),
    monthlyInstallmentAmount: 333333,
    conversionFee: 0,
  );

  final EmiPackageEntity secondEmiPackage = EmiPackageEntity(
    offer: EmiTenorOfferEntity(id: '2', tenor: 6, interestRate: 1.5),
    monthlyInstallmentAmount: 170000,
    conversionFee: 20000,
  );

  final List<EmiPackageEntity> emiPackages = <EmiPackageEntity>[
    firstEmiPackage,
    secondEmiPackage,
  ];

  final VoucherEntity selectedVoucher = VoucherEntity(id: 1234, code: 'TEST_VOUCHER');

  final EmiOptionUiModel emiOptionUiModel = EmiOptionUiModel(
    orderSessionEntity: orderSessionEntity,
    emiPackages: emiPackages,
    selectedEmiPackage: firstEmiPackage,
    selectedVoucher: selectedVoucher,
  );

  void setupMocks() {
    // Mock EmiOptionScreenCubit with stream
    setupCubit<EmiOptionScreenState>(
        mockEmiOptionScreenCubit, EmiOrderInfoLoadedState(orderUiOptionModel: emiOptionUiModel));

    when(() => mockEmiOptionScreenCubit.initialize(
          orderSessionEntity: any(named: 'orderSessionEntity'),
          emiPackages: any(named: 'emiPackages'),
          selectedVoucher: any(named: 'selectedVoucher'),
        )).thenAnswer((_) {});
    when(() => mockEmiOptionScreenCubit.getCachedData(any())).thenReturn(null);
    when(() => mockEmiOptionScreenCubit.updateSelectedEmiPackage(
          orderSessionEntity: any(named: 'orderSessionEntity'),
          selectedEmiPackage: any(named: 'selectedEmiPackage'),
          selectedVoucher: any(named: 'selectedVoucher'),
        )).thenAnswer((_) {});
    when(() => mockEmiOptionScreenCubit.updateEmiPackageFromCacheIfNeeded(
          orderSession: any(named: 'orderSession'),
          selectedEmiPackage: any(named: 'selectedEmiPackage'),
          selectedVoucher: any(named: 'selectedVoucher'),
        )).thenAnswer((_) async {});
    when(() => mockEmiOptionScreenCubit.handleCachingData(
          orderSession: any(named: 'orderSession'),
          emiPackage: any(named: 'emiPackage'),
        )).thenAnswer((_) {});
    when(() => mockEmiOptionScreenCubit.removeSelectedVoucher(
          selectedPackage: any(named: 'selectedPackage'),
        )).thenAnswer((_) {});

    // Mock UpdateOrderCubit with stream
    setupCubit<UpdateOrderState>(mockUpdateOrderCubit, UpdateOrderInitial());

    when(() => mockUpdateOrderCubit.updateOrderPackage(
          orderSession: any(named: 'orderSession'),
          selectedEmiPackage: any(named: 'selectedEmiPackage'),
          selectedVoucher: any(named: 'selectedVoucher'),
        )).thenAnswer((_) async {});
    when(() => mockUpdateOrderCubit.changeToOutrightPayment(
          orderSession: any(named: 'orderSession'),
          selectedEmiPackage: any(named: 'selectedEmiPackage'),
          selectedVoucher: any(named: 'selectedVoucher'),
        )).thenAnswer((_) async {});
    when(() => mockUpdateOrderCubit.removeVoucher(
          orderSession: any(named: 'orderSession'),
          emiPackageEntity: any(named: 'emiPackageEntity'),
        )).thenAnswer((_) async {});

    // Mock PaymentWithEMIUtils
    when(() => mockPaymentWithEMIUtils.onShowTenorInfo(any())).thenAnswer((_) {});

    // Mock EvoUiUtils
    when(() => EvoUiUtils().calculateVerticalSpace(
          context: any(named: 'context'),
          heightPercentage: any(named: 'heightPercentage'),
        )).thenReturn(100);

    setupMockDialogHelper();
  }

  setUpAll(() {
    registerFallbackValue(OrderSessionEntity());
    registerFallbackValue(EmiPackageEntity());
    registerFallbackValue(VoucherEntity());
    registerFallbackValue(ErrorUIModel());
  });

  setUp(() {
    EvoAuthenticationHelper.setInstanceForTesting(MockEvoAuthenticationHelper());

    mockEmiOptionScreenCubit = MockEmiOptionScreenCubit();
    mockUpdateOrderCubit = MockUpdateOrderCubit();
    mockPaymentWithEMIUtils = MockPaymentWithEMIUtils();

    // Setup utils for page state
    initConfigEvoPageStateBase();
    setUpMockConfigEvoPageStateBase();
    setupMockImageProvider();
    setUpOneLinkDeepLinkRegExForTest();

    // Override getIt registrations
    getIt.registerFactory<EmiOptionScreenCubit>(() => mockEmiOptionScreenCubit);
    getIt.registerFactory<UpdateOrderCubit>(() => mockUpdateOrderCubit);
    getIt.registerFactory<PaymentWithEMIUtils>(() => mockPaymentWithEMIUtils);
    getIt.registerLazySingleton<FeatureToggle>(() => FeatureToggle());

    when(() => commonUtilFunction.clearDataOnTokenInvalid(
          clearAllNotifications: any(named: 'clearAllNotifications'),
        )).thenAnswer((_) async => Future<void>.value());

    setupMocks();
    setUpMockSnackBarForTest();
  });

  tearDown(() {
    // Reset GetIt registrations after each test
    getIt.reset();
  });

  Future<void> pumpEmiOptionScreen(WidgetTester tester) async {
    await tester.pumpWidget(
      MaterialApp(
        home: EmiOptionScreen(
          arg: EmiOptionScreenArg(
            orderSession: orderSessionEntity,
            emiPackages: emiPackages,
            selectedVoucher: selectedVoucher,
          ),
        ),
      ),
    );
    await tester.pumpAndSettle();
  }

  EmiOptionsScreenState getScreenState(WidgetTester tester) {
    return tester.state(find.byType(EmiOptionScreen)) as EmiOptionsScreenState;
  }

  group('EmiOptionScreen test pushName method', () {
    test('pushName', () {
      EmiOptionScreen.pushNamed(orderSession: orderSessionEntity, emiPackages: emiPackages);
      verify(() => getIt<CommonNavigator>().pushNamed(
            any(),
            Screen.emiOptionScreen.name,
            extra: any(named: 'extra'),
          )).called(1);
    });
  });

  group('EmiOptionScreen initialization', () {
    testWidgets('should initialize correctly', (WidgetTester tester) async {
      await pumpEmiOptionScreen(tester);
      await tester.pumpAndSettle(Duration(milliseconds: 100));
      // Verify cubit initialization
      verify(() => mockEmiOptionScreenCubit.initialize(
            orderSessionEntity: orderSessionEntity,
            emiPackages: emiPackages,
            selectedVoucher: selectedVoucher,
          )).called(1);

      // Verify UI components are rendered
      expect(find.byType(EmiPaymentTitle), findsOneWidget);
      expect(find.byType(EmiTenorListWidget), findsOneWidget);
      expect(find.byType(EmiSummaryInfoWidget), findsOneWidget);
      expect(find.byType(CTAWithPoweredByWidget), findsOneWidget);
      expect(find.text(EvoStrings.continueBtn), findsOneWidget);
      await tester.pumpWidget(SizedBox());
    });
  });

  group('EmiOptionScreen UI interactions', () {
    testWidgets('should handle tenor selection correctly', (WidgetTester tester) async {
      await pumpEmiOptionScreen(tester);
      final EmiOptionsScreenState state = getScreenState(tester);

      // Simulate tenor selection
      state.handleOnTenorTap(emiOrder: emiOptionUiModel, emiPackage: secondEmiPackage);

      // Verify the correct method was called
      verify(() => mockEmiOptionScreenCubit.updateEmiPackageFromCacheIfNeeded(
            orderSession: emiOptionUiModel.orderSessionEntity,
            selectedEmiPackage: secondEmiPackage,
            selectedVoucher: emiOptionUiModel.selectedVoucher,
          )).called(1);
    });

    testWidgets('should handle continue button press correctly', (WidgetTester tester) async {
      await pumpEmiOptionScreen(tester);
      final EmiOptionsScreenState state = getScreenState(tester);

      // Find and tap the continue button
      await tester.tap(find.text(EvoStrings.continueBtn));
      await tester.pump();

      // Verify isContinueCTAPressed flag is set
      expect(state.isContinueCTAPressed, true);

      // Verify updateOrderPackage is called
      verify(() => mockUpdateOrderCubit.updateOrderPackage(
            orderSession: any(named: 'orderSession'),
            selectedEmiPackage: any(named: 'selectedEmiPackage'),
            selectedVoucher: any(named: 'selectedVoucher'),
          )).called(1);
    });

    testWidgets('should handle onApplyEmiPackageClick with EmiOrderInfoLoadedState correctly',
        (WidgetTester tester) async {
      await pumpEmiOptionScreen(tester);
      final EmiOptionsScreenState state = getScreenState(tester);

      // Mock EmiOptionScreenCubit state
      when(() => mockEmiOptionScreenCubit.state).thenReturn(
        EmiOrderInfoLoadedState(orderUiOptionModel: emiOptionUiModel),
      );

      // Mock getCachedData
      when(() => mockEmiOptionScreenCubit.getCachedData(any())).thenReturn(
        EmiOptionCacheDataModel(
          orderSession: orderSessionEntity,
          selectedEmiPackage: firstEmiPackage,
        ),
      );

      // Call onApplyEmiPackageClick
      state.onApplyEmiPackageClick();

      // Verify getCachedData is called
      verify(() => mockEmiOptionScreenCubit.getCachedData(emiOptionUiModel.selectedEmiPackage))
          .called(1);

      // Verify updateOrderPackage is called with correct parameters
      verify(() => mockUpdateOrderCubit.updateOrderPackage(
            orderSession: orderSessionEntity,
            selectedEmiPackage: firstEmiPackage,
            selectedVoucher: selectedVoucher,
          )).called(1);
    });

    testWidgets('should handle onApplyEmiPackageClick with UpdateSelectedTenorState correctly',
        (WidgetTester tester) async {
      await pumpEmiOptionScreen(tester);
      final EmiOptionsScreenState state = getScreenState(tester);

      // Mock EmiOptionScreenCubit state
      when(() => mockEmiOptionScreenCubit.state).thenReturn(
        UpdateSelectedTenorState(
          orderSessionEntity: orderSessionEntity,
          selectedEmiPackage: secondEmiPackage,
          selectedVoucher: selectedVoucher,
        ),
      );

      // Mock getCachedData
      when(() => mockEmiOptionScreenCubit.getCachedData(any())).thenReturn(
        EmiOptionCacheDataModel(
          orderSession: orderSessionEntity,
          selectedEmiPackage: secondEmiPackage,
        ),
      );

      // Call onApplyEmiPackageClick
      state.onApplyEmiPackageClick();

      // Verify getCachedData is called
      verify(() => mockEmiOptionScreenCubit.getCachedData(secondEmiPackage)).called(1);

      // Verify updateOrderPackage is called with correct parameters
      verify(() => mockUpdateOrderCubit.updateOrderPackage(
            orderSession: orderSessionEntity,
            selectedEmiPackage: secondEmiPackage,
            selectedVoucher: selectedVoucher,
          )).called(1);
    });
  });

  group('EmiOptionScreen state handling', () {
    testWidgets('should handle UpdateOrderLoading state correctly', (WidgetTester tester) async {
      await pumpEmiOptionScreen(tester);
      final EmiOptionsScreenState state = getScreenState(tester);

      // Simulate UpdateOrderLoading state
      state.handleUpdateOrderState(UpdateOrderLoading());

      // Verify loading indicator is shown
      verify(() => EvoUiUtils().showHudLoading()).called(1);
    });

    testWidgets('should handle UpdateOrderError state correctly', (WidgetTester tester) async {
      await pumpEmiOptionScreen(tester);
      final EmiOptionsScreenState state = getScreenState(tester);

      // Simulate UpdateOrderLoading state
      state.handleUpdateOrderState(UpdateOrderError(ErrorUIModel()));

      verify(() => getIt<EvoSnackBar>().show(
            any(),
            typeSnackBar: any(named: 'typeSnackBar'),
            durationInSec: any(named: 'durationInSec'),
            description: any(named: 'description'),
            marginBottomRatio: any(named: 'marginBottomRatio'),
          )).called(1);
    });

    testWidgets('should handle UpdateOrderExpired state correctly', (WidgetTester tester) async {
      await pumpEmiOptionScreen(tester);
      final EmiOptionsScreenState state = getScreenState(tester);

      // Simulate UpdateOrderLoading state
      state.handleUpdateOrderState(UpdateOrderExpired(error: ErrorUIModel()));

      // Verify loading indicator is shown
      verify(() => EvoDialogHelper().showDialogBottomSheet(
            header: any(named: 'header'),
            title: any(named: 'title'),
            titleTextStyle: any(named: 'titleTextStyle'),
            dialogId: any(named: 'dialogId'),
            isShowButtonClose: any(named: 'isShowButtonClose'),
            onClickClose: any(named: 'onClickClose'),
            textPositive: any(named: 'textPositive'),
            titleTextAlign: any(named: 'titleTextAlign'),
            contentSpacing: any(named: 'contentSpacing'),
            positiveButtonStyle: any(named: 'positiveButtonStyle'),
            contentTextStyle: any(named: 'contentTextStyle'),
            footer: any(named: 'footer'),
            onClickPositive: any(named: 'onClickPositive'),
            isDismissible: any(named: 'isDismissible'),
            content: any(named: 'content'),
            textNegative: any(named: 'textNegative'),
            buttonListOrientation: any(named: 'buttonListOrientation'),
            negativeButtonStyle: any(named: 'negativeButtonStyle'),
            onClickNegative: any(named: 'onClickNegative'),
          )).called(1);
    });

    testWidgets('should handle UpdateOrderSuccess state correctly', (WidgetTester tester) async {
      await pumpEmiOptionScreen(tester);
      final EmiOptionsScreenState state = getScreenState(tester);

      // Set isContinueCTAPressed to true to simulate continue button press
      state.isContinueCTAPressed = true;

      // Simulate UpdateOrderSuccess state
      state.handleUpdateOrderState(UpdateOrderSuccess(
        orderSession: orderSessionEntity,
        emiPackage: firstEmiPackage,
        selectedVoucher: selectedVoucher,
      ));

      // Verify loading indicator is hidden
      verify(() => EvoUiUtils().hideHudLoading()).called(2);

      // Verify removeSelectedVoucher is called
      verify(() => mockEmiOptionScreenCubit.removeSelectedVoucher(
            selectedPackage: firstEmiPackage,
          )).called(1);
    });

    testWidgets('should handle UpdateOrderInvalidVoucher state correctly',
        (WidgetTester tester) async {
      await pumpEmiOptionScreen(tester);
      final EmiOptionsScreenState state = getScreenState(tester);

      // Set isContinueCTAPressed to true to simulate continue button press
      state.isContinueCTAPressed = true;

      // Simulate UpdateOrderInvalidVoucher state
      state.handleUpdateOrderState(UpdateOrderInvalidVoucher(
        error: ErrorUIModel(userMessage: 'Invalid voucher'),
        orderSession: orderSessionEntity,
        emiPackage: firstEmiPackage,
        selectedVoucher: selectedVoucher,
      ));

      // Verify loading indicator is hidden
      verify(() => EvoUiUtils().hideHudLoading()).called(2);
    });

    testWidgets(
        'should handle UpdateOrderInvalidVoucher state with isContinueCTAPressed=false correctly',
        (WidgetTester tester) async {
      await pumpEmiOptionScreen(tester);
      final EmiOptionsScreenState state = getScreenState(tester);

      // Set isContinueCTAPressed to false
      state.isContinueCTAPressed = false;

      // Simulate UpdateOrderInvalidVoucher state
      state.handleUpdateOrderState(UpdateOrderInvalidVoucher(
        error: ErrorUIModel(userMessage: 'Invalid voucher'),
        orderSession: orderSessionEntity,
        emiPackage: firstEmiPackage,
        selectedVoucher: selectedVoucher,
      ));

      // Verify loading indicator is hidden
      verify(() => EvoUiUtils().hideHudLoading()).called(2);

      // Verify updateCachingData is called
      verify(() => mockEmiOptionScreenCubit.handleCachingData(
            orderSession: orderSessionEntity,
            emiPackage: firstEmiPackage,
          )).called(1);
    });

    testWidgets('should handle UpdateOrderUnqualifiedAfterApplyVoucher state correctly',
        (WidgetTester tester) async {
      await pumpEmiOptionScreen(tester);
      final EmiOptionsScreenState state = getScreenState(tester);

      // Simulate UpdateOrderUnqualifiedAfterApplyVoucher state
      state.handleUpdateOrderState(UpdateOrderUnqualifiedAfterApplyVoucher(
        error: ErrorUIModel(userMessage: 'Unqualified voucher'),
        orderSession: orderSessionEntity,
        emiPackage: firstEmiPackage,
        selectedVoucher: selectedVoucher,
      ));

      // Verify loading indicator is hidden
      verify(() => EvoUiUtils().hideHudLoading()).called(2);
    });

    testWidgets('should handle ChangeOrderToOutrightPaymentSuccess state correctly',
        (WidgetTester tester) async {
      await pumpEmiOptionScreen(tester);
      final EmiOptionsScreenState state = getScreenState(tester);

      // Simulate ChangeOrderToOutrightPaymentSuccess state
      state.handleUpdateOrderState(ChangeOrderToOutrightPaymentSuccess(
        orderSession: orderSessionEntity,
        selectedVoucher: selectedVoucher,
      ));

      // Verify loading indicator is hidden
      verify(() => EvoUiUtils().hideHudLoading()).called(2);

      // Verify navigation methods are called
      verify(() => getIt<CommonNavigator>().pushNamed(
            any(),
            Screen.updateConfirmPaymentScreen.name,
            extra: any(named: 'extra'),
          )).called(1);
    });

    testWidgets('should handle GetEmiOrderFromCacheSuccessState correctly',
        (WidgetTester tester) async {
      await pumpEmiOptionScreen(tester);
      final EmiOptionsScreenState state = getScreenState(tester);

      // Simulate GetEmiOrderFromCacheSuccessState
      state.handleEmiOptionScreenStateChanged(GetEmiOrderFromCacheSuccessState(
        orderSession: orderSessionEntity,
        emiPackage: secondEmiPackage,
        selectedVoucher: selectedVoucher,
      ));

      // Verify updateSelectedEmiPackage is called
      verify(() => mockEmiOptionScreenCubit.updateSelectedEmiPackage(
            orderSessionEntity: orderSessionEntity,
            selectedEmiPackage: secondEmiPackage,
            selectedVoucher: selectedVoucher,
          )).called(1);
    });

    testWidgets('should handle NeededUpdateEmiOrderState correctly', (WidgetTester tester) async {
      await pumpEmiOptionScreen(tester);
      final EmiOptionsScreenState state = getScreenState(tester);

      // Simulate NeededUpdateEmiOrderState
      state.handleEmiOptionScreenStateChanged(NeededUpdateEmiOrderState(
        orderSession: orderSessionEntity,
        emiPackage: secondEmiPackage,
        selectedVoucher: selectedVoucher,
      ));

      // Verify updateOrderPackage is called
      verify(() => mockUpdateOrderCubit.updateOrderPackage(
            orderSession: orderSessionEntity,
            selectedEmiPackage: secondEmiPackage,
            selectedVoucher: selectedVoucher,
          )).called(1);
    });
  });

  group('EmiOptionScreen helper methods', () {
    testWidgets('should handle getTenorInfo correctly', (WidgetTester tester) async {
      await pumpEmiOptionScreen(tester);
      final EmiOptionsScreenState state = getScreenState(tester);

      // Mock state with information
      final List<String> tenorInfo = <String>['Info 1', 'Info 2'];
      when(() => mockEmiOptionScreenCubit.state).thenReturn(
        EmiOrderInfoLoadedState(
          orderUiOptionModel: EmiOptionUiModel(
            orderSessionEntity: orderSessionEntity,
            emiPackages: emiPackages,
            selectedEmiPackage: EmiPackageEntity(
              offer: EmiTenorOfferEntity(
                information: tenorInfo,
              ),
            ),
          ),
        ),
      );

      // Call getTenorInfo
      final List<String>? result = state.getTenorInfo();

      // Verify result
      expect(result, tenorInfo);

      when(() => mockEmiOptionScreenCubit.state).thenReturn(
        UpdateSelectedTenorState(orderSessionEntity: null, selectedEmiPackage: null),
      );

      expect(state.getTenorInfo(), null);
    });

    testWidgets('should handle updateCachingData correctly', (WidgetTester tester) async {
      await pumpEmiOptionScreen(tester);
      final EmiOptionsScreenState state = getScreenState(tester);

      // Call updateCachingData
      state.updateCachingData(
        orderSession: orderSessionEntity,
        emiPackage: firstEmiPackage,
      );

      // Verify handleCachingData is called
      verify(() => mockEmiOptionScreenCubit.handleCachingData(
            orderSession: orderSessionEntity,
            emiPackage: firstEmiPackage,
          )).called(1);
    });

    testWidgets('should handle onPayEmiWithoutVoucher correctly', (WidgetTester tester) async {
      await pumpEmiOptionScreen(tester);
      final EmiOptionsScreenState state = getScreenState(tester);

      // Call onPayEmiWithoutVoucher
      state.onPayEmiWithoutVoucher(
        orderSession: orderSessionEntity,
        emiPackage: firstEmiPackage,
      );

      // Verify removeVoucher is called
      verify(() => mockUpdateOrderCubit.removeVoucher(
            orderSession: orderSessionEntity,
            emiPackageEntity: firstEmiPackage,
          )).called(1);
    });

    testWidgets('should handle onChangeToOutRightPayment correctly', (WidgetTester tester) async {
      await pumpEmiOptionScreen(tester);
      final EmiOptionsScreenState state = getScreenState(tester);

      // Call onChangeToOutRightPayment
      state.onChangeToOutRightPayment(
        orderSession: orderSessionEntity,
        selectedEmiPackage: firstEmiPackage,
        selectedVoucher: selectedVoucher,
      );

      // Verify changeToOutrightPayment is called
      verify(() => mockUpdateOrderCubit.changeToOutrightPayment(
            orderSession: orderSessionEntity,
            selectedEmiPackage: firstEmiPackage,
            selectedVoucher: selectedVoucher,
          )).called(1);
    });

    testWidgets('should handle handleChangeToOutrightSuccess correctly',
        (WidgetTester tester) async {
      await pumpEmiOptionScreen(tester);
      final EmiOptionsScreenState state = getScreenState(tester);

      // Call handleChangeToOutrightSuccess
      state.handleChangeToOutrightSuccess(
        orderSession: orderSessionEntity,
        selectedVoucher: selectedVoucher,
      );

      verify(() => getIt<CommonNavigator>().pushNamed(
            any(),
            Screen.updateConfirmPaymentScreen.name,
            extra: any(named: 'extra'),
          )).called(1);
    });

    testWidgets('should handle showUnqualifiedEmiErrorDialog correctly',
        (WidgetTester tester) async {
      await pumpEmiOptionScreen(tester);
      final EmiOptionsScreenState state = getScreenState(tester);

      // Call showUnqualifiedEmiErrorDialog
      state.showUnqualifiedEmiErrorDialog(UpdateOrderUnqualifiedAfterApplyVoucher(
        error: ErrorUIModel(userMessage: 'Unqualified voucher'),
        orderSession: orderSessionEntity,
        emiPackage: firstEmiPackage,
        selectedVoucher: selectedVoucher,
      ));

      // Verify dialog is shown
      verify(() => EvoDialogHelper().showDialogBottomSheet(
            header: any(named: 'header'),
            title: any(named: 'title'),
            titleTextStyle: any(named: 'titleTextStyle'),
            dialogId: any(named: 'dialogId'),
            isShowButtonClose: any(named: 'isShowButtonClose'),
            onClickClose: any(named: 'onClickClose'),
            textPositive: any(named: 'textPositive'),
            titleTextAlign: any(named: 'titleTextAlign'),
            contentSpacing: any(named: 'contentSpacing'),
            positiveButtonStyle: any(named: 'positiveButtonStyle'),
            footer: any(named: 'footer'),
            onClickPositive: any(named: 'onClickPositive'),
            isDismissible: any(named: 'isDismissible'),
            content: any(named: 'content'),
            textNegative: any(named: 'textNegative'),
            buttonListOrientation: any(named: 'buttonListOrientation'),
            negativeButtonStyle: any(named: 'negativeButtonStyle'),
            onClickNegative: any(named: 'onClickNegative'),
          )).called(1);
    });

    testWidgets('should handle showInvalidVoucherDialog correctly', (WidgetTester tester) async {
      await pumpEmiOptionScreen(tester);
      final EmiOptionsScreenState state = getScreenState(tester);

      // Call showInvalidVoucherDialog
      state.showInvalidVoucherDialog(UpdateOrderInvalidVoucher(
        error: ErrorUIModel(userMessage: 'Invalid voucher'),
        orderSession: orderSessionEntity,
        emiPackage: firstEmiPackage,
        selectedVoucher: selectedVoucher,
      ));

      // Verify dialog is shown
      verify(() => EvoDialogHelper().showDialogBottomSheet(
            header: any(named: 'header'),
            title: any(named: 'title'),
            titleTextStyle: any(named: 'titleTextStyle'),
            dialogId: any(named: 'dialogId'),
            isShowButtonClose: any(named: 'isShowButtonClose'),
            onClickClose: any(named: 'onClickClose'),
            textPositive: any(named: 'textPositive'),
            titleTextAlign: any(named: 'titleTextAlign'),
            contentSpacing: any(named: 'contentSpacing'),
            positiveButtonStyle: any(named: 'positiveButtonStyle'),
            footer: any(named: 'footer'),
            onClickPositive: any(named: 'onClickPositive'),
            isDismissible: any(named: 'isDismissible'),
            content: any(named: 'content'),
            textNegative: any(named: 'textNegative'),
            buttonListOrientation: any(named: 'buttonListOrientation'),
            negativeButtonStyle: any(named: 'negativeButtonStyle'),
            onClickNegative: any(named: 'onClickNegative'),
          )).called(1);
    });
  });
}
