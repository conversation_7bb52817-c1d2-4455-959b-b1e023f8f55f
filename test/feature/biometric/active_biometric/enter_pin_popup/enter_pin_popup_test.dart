import 'package:evoapp/data/repository/user_repo.dart';
import 'package:evoapp/data/response/biometric_token_entity.dart';
import 'package:evoapp/feature/biometric/activate_biometric/enter_pin_popup/enter_pin_popup.dart';
import 'package:evoapp/feature/biometric/activate_biometric/enter_pin_popup/enter_pin_popup_cubit.dart';
import 'package:evoapp/feature/biometric/biometric_token_module/extra_biometric_challenge.dart';
import 'package:evoapp/feature/feature_toggle.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/util/mock_file_name_utils/mock_user_file_name.dart';
import 'package:evoapp/util/ui_utils/evo_ui_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/util/common_navigator.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../base/evo_page_state_base_test_config.dart';
import '../../../../util/flutter_test_config.dart';
import '../../../../util/test_util.dart';

class MockUserRepo extends Mock implements UserRepo {}

class MockBiometricChallengeCallback extends Mock implements BiometricChallengeCallback {}

void main() {
  late MockUserRepo mockUserRepo;
  late MockBiometricChallengeCallback mockCallback;

  setUpAll(() {
    TestWidgetsFlutterBinding.ensureInitialized();
    mockNavigatorContext = MockBuildContext();
    mockUserRepo = MockUserRepo();
    mockCallback = MockBiometricChallengeCallback();
    registerFallbackValue(BoxFit.contain);
    registerFallbackValue(MockBuildContext());
    EvoUiUtils.setInstanceForTesting(MockEvoUiUtils());
    when(() => EvoUiUtils().showHudLoading()).thenAnswer((_) async => Future<void>.value());
    when(() => EvoUiUtils().hideHudLoading()).thenAnswer((_) async => Future<void>.value());
  });

  setUp(() {
    getIt.registerLazySingleton<FeatureToggle>(() => FeatureToggle());
    getItRegisterTextStyle();
    getItRegisterColor();
    getItRegisterButtonStyle();
    getItRegisterMockCommonUtilFunctionAndImageProvider();
    setupMockImageProvider();
    setUpMockGlobalKeyProvider(mockNavigatorContext);
    getIt.registerLazySingleton<CommonNavigator>(() => MockCommonNavigator());
    getIt.registerLazySingleton<UserRepo>(() => mockUserRepo);
    when(() => getIt<CommonNavigator>().pop(
          any(),
        )).thenAnswer((_) => Future<void>.value());
  });

  tearDown(() {
    getIt.reset();
  });

  Widget createTestWidget() {
    return MaterialApp(
      home: Scaffold(
        body: EnterPinPopup(callback: mockCallback),
      ),
    );
  }

  group('EnterPinPopup Widget Tests', () {
    testWidgets('should render EnterPinPopup widget correctly', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());
      await tester.pump(Duration(milliseconds: 100));

      expect(find.byType(EnterPinPopup), findsOneWidget);
    });

    testWidgets('should show PIN input fields', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());
      await tester.pump(Duration(milliseconds: 100));

      // Look for PIN input fields (assuming they are TextFormField or similar)
      expect(find.byType(TextFormField), findsWidgets);
    });

    testWidgets('should handle PIN input and verification', (WidgetTester tester) async {
      const String testPin = '123456';

      // Setup mock response for successful PIN verification
      final Map<String, dynamic> responseData =
          await TestUtil.getResponseMock(getBiometricTokenByPinMockFileName(pin: testPin));

      when(() => mockUserRepo.getBiometricTokenByPin(
            pin: testPin,
            mockConfig: any(named: 'mockConfig'),
          )).thenAnswer((_) async => BiometricTokenEntity.fromBaseResponse(BaseResponse(
            statusCode: CommonHttpClient.SUCCESS,
            response: responseData,
          )));

      await tester.pumpWidget(createTestWidget());
      await tester.pump(Duration(milliseconds: 100));

      // Find and enter PIN (this might need adjustment based on actual UI structure)
      final Finder pinFields = find.byType(TextFormField);
      if (pinFields.evaluate().isNotEmpty) {
        await tester.enterText(pinFields.first, testPin);
        await tester.pump(Duration(milliseconds: 400));
      }

      // Verify that the PIN was entered
      expect(find.text(testPin), findsAtLeastNWidgets(1));
    });


    testWidgets('should toggle PIN visibility', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());
      await tester.pump(Duration(milliseconds: 100));
      final EnterPinWidgetState screenState = tester.state(find.byType(EnterPinPopup)) as EnterPinWidgetState;
      final EnterPinPopupCubit enterPinPopupCubit = screenState.enterPinPopupCubit;
      // Test PIN visibility toggle functionality
      enterPinPopupCubit.toggleObscurePinText();
      await tester.pump(Duration(milliseconds: 100));

      // Verify that the obscure state changed
      expect(enterPinPopupCubit.state, isA<ObscureEnterPinState>());
    });
  });
}
