import 'package:evoapp/feature/biometric_pin_confirm/confirm_pin/confirm_pin_popup.dart';
import 'package:evoapp/feature/biometric_pin_confirm/confirm_pin/confirm_pin_popup_cubit.dart';
import 'package:evoapp/feature/feature_toggle.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/util/ui_utils/evo_ui_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/util/common_navigator.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../base/evo_page_state_base_test_config.dart';
import '../../../util/flutter_test_config.dart';

class MockConfirmPinPopupCallback extends Mock implements ConfirmPinPopupCallback {}

void main() {
  late MockConfirmPinPopupCallback mockCallback;

  setUpAll(() {
    TestWidgetsFlutterBinding.ensureInitialized();
    mockNavigatorContext = MockBuildContext();
    registerFallbackValue(BoxFit.contain);
    registerFallbackValue(MockBuildContext());
    EvoUiUtils.setInstanceForTesting(MockEvoUiUtils());
    when(() => EvoUiUtils().showHudLoading()).thenAnswer((_) async => Future<void>.value());
    when(() => EvoUiUtils().hideHudLoading()).thenAnswer((_) async => Future<void>.value());
  });

  setUp(() {
    getIt.registerLazySingleton<FeatureToggle>(() => FeatureToggle());
    mockCallback = MockConfirmPinPopupCallback();
    getItRegisterTextStyle();
    getItRegisterColor();
    getItRegisterButtonStyle();
    getItRegisterMockCommonUtilFunctionAndImageProvider();
    setupMockImageProvider();
    setUpMockGlobalKeyProvider(mockNavigatorContext);
    getIt.registerLazySingleton<CommonNavigator>(() => MockCommonNavigator());
    when(() => getIt<CommonNavigator>().pop(
          any(),
        )).thenAnswer((_) => Future<void>.value());
  });

  tearDown(() {
    getIt.reset();
  });

  Widget createTestWidget() {
    return MaterialApp(
      home: Scaffold(
        body: ConfirmPinPopup(callback: mockCallback),
      ),
    );
  }

  group('ConfirmPinPopup Widget Tests', () {
    testWidgets('should render ConfirmPinPopup widget correctly', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());
      await tester.pump(Duration(milliseconds: 100));

      expect(find.byType(ConfirmPinPopup), findsOneWidget);
    });

    testWidgets('should show PIN input fields', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());
      await tester.pump(Duration(milliseconds: 100));

      // Look for PIN input fields (assuming they are TextFormField or similar)
      expect(find.byType(TextFormField), findsWidgets);
    });

    testWidgets('should handle PIN input and submission', (WidgetTester tester) async {
      const String testPin = '123456';

      await tester.pumpWidget(createTestWidget());
      await tester.pump(Duration(milliseconds: 100));

      // Find and enter PIN (this might need adjustment based on actual UI structure)
      final Finder pinFields = find.byType(TextFormField);
      if (pinFields.evaluate().isNotEmpty) {
        await tester.enterText(pinFields.first, testPin);
        await tester.pump(Duration(milliseconds: 400));
      }

      // Verify that the PIN was entered
      expect(find.text(testPin), findsAtLeastNWidgets(1));
    });

    testWidgets('should call callback when PIN is submitted', (WidgetTester tester) async {
      const String testPin = '123456';

      await tester.pumpWidget(createTestWidget());
      await tester.pump(Duration(milliseconds: 100));

      final ConfirmPinWidgetState screenState =
          tester.state(find.byType(ConfirmPinPopup)) as ConfirmPinWidgetState;

      // Simulate PIN submission
      screenState.onPinSubmit(testPin);
      await tester.pump(Duration(milliseconds: 100));

      // Verify that the callback was called with the correct PIN
      verify(() => mockCallback.onInputPin(testPin)).called(1);
    });

    testWidgets('should toggle PIN visibility', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());
      await tester.pump(Duration(milliseconds: 100));

      final ConfirmPinWidgetState screenState =
          tester.state(find.byType(ConfirmPinPopup)) as ConfirmPinWidgetState;
      final ConfirmPinPopupCubit confirmPinPopupCubit = screenState.enterPinPopupCubit;

      // Test PIN visibility toggle functionality
      confirmPinPopupCubit.toggleObscurePinText();
      await tester.pump(Duration(milliseconds: 100));

      // Verify that the obscure state changed
      expect(confirmPinPopupCubit.state, isA<ObscureEnterPinState>());
    });

    testWidgets('should close popup when tapping outside content area',
        (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());
      await tester.pump(Duration(milliseconds: 100));

      // Find the transparent area outside the content
      final Finder transparentArea = find.byType(GestureDetector).first;

      // Tap outside the content area
      await tester.tap(transparentArea);
      await tester.pump(Duration(milliseconds: 100));

      // Verify that navigator.pop was called
      verify(() => getIt<CommonNavigator>().pop(any())).called(1);
    });

    testWidgets('should show visibility toggle button', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());
      await tester.pump(Duration(milliseconds: 100));

      // Look for the visibility toggle button (InkWell with eye icon)
      expect(find.byType(InkWell), findsAtLeastNWidgets(1));
    });
  });
}
