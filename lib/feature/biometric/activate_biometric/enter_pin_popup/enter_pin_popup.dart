import 'package:flutter/material.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/resources/resources.dart';
import 'package:flutter_common_package/util/utils.dart';
import 'package:flutter_common_package/widget/pin_code/common_pin_code_field_shape.dart';
import 'package:flutter_common_package/widget/pin_code/common_pin_theme.dart';
import 'package:flutter_common_package/widget/pin_code/pin_code_widget.dart';

import '../../../../data/repository/user_repo.dart';
import '../../../../prepare_for_app_initiation.dart';
import '../../../../resources/resources.dart';
import '../../../../util/ui_utils/evo_ui_utils.dart';
import '../../../../widget/default_obscure_widget.dart';
import '../../../../widget/evo_pin_code/evo_pin_code_config.dart';
import '../../../feature_toggle.dart';
import '../../base/biometric_token_cubit.dart';
import '../../biometric_token_module/extra_biometric_challenge.dart';
import 'enter_pin_popup_cubit.dart';

class EnterPinPopup extends BiometricChallengeWidget {
  const EnterPinPopup({super.key, super.callback});

  @override
  State<EnterPinPopup> createState() => EnterPinWidgetState();

  static void show(BuildContext context, {BiometricChallengeCallback? callback}) {}
}

@visibleForTesting
class EnterPinWidgetState extends BiometricChallengeState<EnterPinPopup> {
  static const double padding = 0;

  @visibleForTesting
  final EnterPinPopupCubit enterPinPopupCubit = EnterPinPopupCubit();

  @visibleForTesting
  final BiometricTokenCubit biometricTokenCubit = BiometricTokenCubit(getIt.get<UserRepo>());

  @visibleForTesting
  TextEditingController pinTextController = TextEditingController();

  @override
  void dispose() {
    widget.callback?.onBioChallengeCancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: <BlocProvider<dynamic>>[
        BlocProvider<EnterPinPopupCubit>(
          create: (_) => enterPinPopupCubit,
        ),
        BlocProvider<BiometricTokenCubit>(
          create: (_) => biometricTokenCubit,
        ),
      ],
      child: LayoutBuilder(
        builder: (BuildContext context, BoxConstraints constraints) {
          return MultiBlocListener(
            listeners: <BlocListener<dynamic, dynamic>>[
              BlocListener<BiometricTokenCubit, BiometricTokenState>(
                listener: (BuildContext context, BiometricTokenState currState) {
                  listenBiometricToken(currState);
                },
              ),
              BlocListener<EnterPinPopupCubit, EnterPinPopupState>(
                  listener: (BuildContext context, EnterPinPopupState currState) {
                listenEnterPinState(currState);
              })
            ],
            child: Padding(
              padding: EdgeInsets.only(
                  bottom: MediaQuery.of(context).viewInsets.bottom +
                      context.screenPadding.bottom +
                      padding),
              child: Column(
                children: <Widget>[
                  Expanded(
                    child: GestureDetector(
                      onTap: () {
                        navigatorContext?.pop();
                      },
                      child: Container(
                        color: Colors.transparent,
                      ),
                    ),
                  ),
                  getContent(),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  @visibleForTesting
  Widget getContent() {
    return BlocBuilder<EnterPinPopupCubit, EnterPinPopupState>(
        buildWhen: (EnterPinPopupState prevState, EnterPinPopupState currState) =>
            currState is! LoadingEnterPinState,
        builder: (BuildContext context, EnterPinPopupState state) {
          bool hasError = false;
          String errMessage = '';
          if (state is ErrorEnterPinState &&
              state.errorUIModel.statusCode != CommonHttpClient.LIMIT_EXCEEDED) {
            hasError = true;
            errMessage = state.errorUIModel.userMessage ?? CommonStrings.otherGenericErrorMessage;
          }

          return Container(
            width: context.screenWidth,
            padding: const EdgeInsets.all(20),
            decoration: const BoxDecoration(
              borderRadius:
                  BorderRadius.only(topLeft: Radius.circular(24), topRight: Radius.circular(24)),
              color: Colors.white,
            ),
            child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: <Widget>[
                  Text(
                    EvoStrings.enterPinTitle,
                    style: evoTextStyles.h600(evoColors.foreground).copyWith(height: 1.2),
                  ),
                  const SizedBox(height: 10),
                  Text(
                    EvoStrings.enterPinToAuthenticate,
                    style: evoTextStyles.h400().copyWith(
                        color: evoColors.textPassive,
                        height: 1.5,
                        fontSize: 16,
                        fontWeight: FontWeight.w400),
                  ),
                  const SizedBox(height: 16),
                  getIt<FeatureToggle>().enableRevampUiFeature
                      ? CommonPinCode(
                          textController: pinTextController,
                          onSubmit: verifyPin,
                          animationDuration: Duration.zero,
                          obscuringWidget: enterPinPopupCubit.obscurePinText
                              ? const DefaultObscureWidget()
                              : null,
                          pinTheme: CommonPinTheme(
                            fieldHeight: EvoPinCodeConfig.defaultPinCodeFieldHeightV2,
                            selectedColor: evoColorsV2.neutral12,
                            shape: CommonPinCodeFieldShape.box,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          autoDismissKeyboard: false,
                          showCursor: true,
                          cursorHeight: 16,
                          cursorWidth: 1,
                          cursorColor: evoColorsV2.primary9,
                          textStyle: TextStyle(fontSize: 18, fontWeight: FontWeight.w500),
                        )
                      : CommonPinCode(
                          pinTheme: CommonPinTheme(
                            fieldHeight: EvoPinCodeConfig.defaultPinCodeFieldHeight,
                          ),
                          textController: pinTextController,
                          onSubmit: verifyPin,
                          animationDuration: Duration.zero,
                          obscuringWidget: enterPinPopupCubit.obscurePinText
                              ? const DefaultObscureWidget()
                              : null,
                          autoDismissKeyboard: false,
                        ),
                  Container(
                    margin: const EdgeInsets.symmetric(vertical: 4),
                    child: Row(
                      children: <Widget>[
                        Expanded(
                            child: Visibility(
                          visible: hasError,
                          child: Padding(
                            padding: const EdgeInsets.only(right: 8.0),
                            child: Text(errMessage,
                                style: EvoTextStyles().bodyMedium(evoColors.error)),
                          ),
                        )),
                        InkWell(
                          onTap: () {
                            enterPinPopupCubit.toggleObscurePinText();
                          },
                          child: Padding(
                            padding: const EdgeInsets.symmetric(vertical: 4.0),
                            child: evoImageProvider.asset(enterPinPopupCubit.obscurePinText
                                ? EvoImages.icShowOffPin
                                : EvoImages.icShowOnPin),
                          ),
                        ),
                      ],
                    ),
                  )
                ]),
          );
        });
  }

  @visibleForTesting
  void verifyPin(String pin) {
    biometricTokenCubit.getBiometricTokenByPin(pin);
  }

  @visibleForTesting
  Future<void> listenBiometricToken(BiometricTokenState currState) async {
    if (currState is BiometricTokenLoadingState) {
      enterPinPopupCubit.showLoading(true);
    }
    if (currState is BiometricTokenErrorState) {
      enterPinPopupCubit.showLoading(false);
      enterPinPopupCubit.handleError(currState.errorUIModel);
    } else if (currState is BiometricTokenSuccessState) {
      enterPinPopupCubit.showLoading(false);
      navigatorContext?.pop();
      widget.callback?.onBioChallengeSuccess(currState.biometricToken);
    }
  }

  @visibleForTesting
  void listenEnterPinState(EnterPinPopupState currState) {
    if (currState is LoadingEnterPinState && currState.isShowLoading) {
      EvoUiUtils().showHudLoading();
      return;
    }

    EvoUiUtils().hideHudLoading();

    if (currState is ErrorEnterPinState) {
      pinTextController.clear();
      if (currState.errorUIModel.statusCode == CommonHttpClient.LIMIT_EXCEEDED ||
          currState.isDismissPopup) {
        widget.callback?.onBioChallengeError(currState.errorUIModel);
        navigatorContext?.pop();
      }
      return;
    }
  }
}
