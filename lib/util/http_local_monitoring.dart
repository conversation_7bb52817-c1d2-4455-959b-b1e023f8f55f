import 'package:chucker_flutter/chucker_flutter.dart';
import 'package:chucker_flutter/src/view/helper/chucker_ui_helper.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:shake_detector/shake_detector.dart';

import '../feature/biometric/activate_biometric/enter_pin_popup/enter_pin_popup.dart';
import '../feature/biometric_pin_confirm/confirm_pin/confirm_pin_popup.dart';
import '../feature/delete_account/verify_pin/delete_account_verify_pin_screen.dart';
import '../feature/login/old_device/login_on_old_device_screen.dart';
import '../feature/pin/input_pin/input_pin_screen.dart';
import '../feature/verify_otp/verify_otp_page.dart';
import '../prepare_for_app_initiation.dart';

class HttpLocalMonitoring {
  static HttpLocalMonitoring? _instance;
  static final HttpLocalMonitoring _originalInstance = HttpLocalMonitoring._internal();

  factory HttpLocalMonitoring() {
    return _instance ??= _originalInstance;
  }

  HttpLocalMonitoring._internal();

  // Method to replace the singleton instance (for testing only)
  static void instanceForTesting(HttpLocalMonitoring instance) {
    _instance = instance;
  }

  // Method to reset the singleton instance (for testing only)
  static void resetToOriginalInstance() {
    _instance = _originalInstance;
  }

  @visibleForTesting
  bool debugMenuShowed = false;

  void init({required bool isDebugMode}) {
    if (isDebugMode) {
      //listen for shake behavior in debug mode, will show debug menu on shake
      ShakeDetector.autoStart(onShake: () => showDebugMenu(isDebugMode: isDebugMode));

      ///it show in app message for both success and fail calls, so turn off to avoid annoying
      ChuckerFlutter.showNotification = false;
      //add interceptors for debug purpose
      final Dio commonDio = getIt.get<Dio>();
      final Dio doeDio = getIt.get<Dio>(instanceName: getItInstanceNameForDOPNative);
      final Dio nonAuthDio = getIt.get<Dio>(instanceName: nonAuthenticationHttpClientInstance);
      commonDio.interceptors.add(ChuckerDioInterceptor());
      doeDio.interceptors.add(ChuckerDioInterceptor());
      nonAuthDio.interceptors.add(ChuckerDioInterceptor());
    }
  }

  @visibleForTesting
  Future<bool> showDebugMenu({required bool isDebugMode}) async {
    if (debugMenuShowed || !isDebugMode) {
      return false;
    }
    final BuildContext? context = navigatorContext;
    if (context != null) {
      debugMenuShowed = true;
      await showDialog<void>(
        context: context,
        builder: (BuildContext context) {
          return AlertDialog(
              title: const Text('Debug Menu'),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: <Widget>[
                  ElevatedButton(
                    onPressed: () {
                      Navigator.pop(context);
                      // DeleteAccountVerifyPinScreen.pushNamed(sessionToken: 'sessionToken');
                      // InputPinScreen.pushNamed(
                      //   phoneNumber: '**********',
                      //   sessionToken: 'sessionToken',
                      //   entryPoint: InputPinEntryPoint.fromDOE,
                      // );
                      // LoginOnOldDeviceScreen.pushNamed();
                      // showModalBottomSheet<void>(
                      //     context: context,
                      //     enableDrag: false,
                      //     isScrollControlled: true,
                      //     backgroundColor: Colors.transparent,
                      //     builder: (_) {
                      //       return EnterPinPopup();
                      //     });
                      // ConfirmPinPopup.show();
                      return;
                      ChuckerUiHelper.showChuckerScreen();
                    },
                    child: const Text('Go to network screen'),
                  ),
                  ElevatedButton(
                    onPressed: () {
                      Navigator.pop(context);
                    },
                    child: const Text('Close'),
                  ),
                ],
              ));
        },
      );
      debugMenuShowed = false;
      return true;
    }
    return false;
  }
}
