class EvoStrings {
  // Common
  static const String footerLoadingText = 'Đang tải';
  static const String confirmText = 'Xác nhận';
  static const String notification = 'Thông báo';
  static const String existWarning = 'Nhấn lần nữa để thoát';
  static const String retry = 'Thử lại';
  static const String active = 'Kích hoạt';
  static const String hubLoadingText = 'Đợi chút nhé';
  static const String doAgain = 'Thực hiện lại';
  static const String understand = 'Đã hiểu';
  static const String groupHeaderMonthTitle = 'Tháng';
  static const String ignore = 'Bỏ qua';
  static const String review = 'Đánh giá';

  // Bottom navigation bar
  static const String bottomBarHomeLabel = 'Trang chủ';
  static const String bottomBarHistoryLabel = 'Lịch sử GD';
  static const String bottomBarRewardLabel = 'Ưu đãi';
  static const String bottomBarAccountLabel = 'Tài khoản';
  static const String bottomBarLoginLabel = 'Đăng nhập';
  static const String bottomBarScanQRLabel = 'Quét QR';

  // Tutorial flow
  static const String tutorialTitle = 'Tiền thêm cho mọi nhà';
  static const String tutorialDescription =
      'Từ mở thẻ tín dụng đến tận hưởng ưu đãi đều ngay tức thì.';
  static const String tutorialLoginButtonText = 'Tiếp tục';
  static const String tutorialSkipButtonText = 'Bỏ qua';

  // Login flow
  static const String prefixTermAndCondition = 'Bằng việc chọn Tiếp tục, tôi đồng ý ';
  static const String termAndCondition = 'điều khoản & điều kiện';
  static const String suffixTermAndCondition =
      ' của EVO và cho phép dùng số điện thoại để nhận mã OTP';
  static const String versionApp = 'v {0}';
  static const String otpInvalidMsg = 'Mã OTP không đúng, vui lòng kiểm tra và nhập lại.';

  static const String loginTitle = 'Đăng nhập/Đăng ký';
  static const String loginDesc = 'Nhập số điện thoại đã đăng ký thẻ EVO của bạn để đăng nhập';

  static const String wrongPhoneFormat = 'Số điện thoại chưa đúng định dạng';
  static const String inputPhoneHint = 'Số điện thoại';

  static const String loginDeactivatedAccountTitle = 'Bạn đã xoá tài khoản này';

  static const String loginInvalidDeviceTokenTitle = 'Nhập OTP để đăng nhập bạn nhé';
  static const String loginInvalidDeviceTokenContent =
      'Để đảm bảo an toàn cho tài khoản, bạn cần nhập lại OTP và PIN để đăng nhập EVO';
  static const String loginInvalidDeviceTokenConfirm = 'Đồng ý';
  static const String doeCompletedWithAnotherPhone =
      'Bạn vừa hoàn tất mở thẻ với số điện thoại khác';
  static const String pleaseLoginToUseEvo =
      'Vui lòng đăng nhập lại để tiếp tục sử dụng ứng dụng EVO.';
  static const String loginAgain = 'Đăng nhập lại';

  //OTP
  static const String otpDescription = 'Nhập mã OTP được gửi tới số thuê bao';

  // Home page
  static const String openCardSuggestion_1 = 'Mở thẻ thành công\n';
  static const String openCardSuggestion_2 = 'giảm giá';
  static const String openCardSuggestion_3 = ' cực đỉnh';
  static const String openCardBtn = 'Mở ngay';
  static const String hotVoucherTitle = 'Ưu đãi hot';
  static const String cardBenefitsTitle = 'Lợi ích thẻ';
  static const String cardBenefit_1 = 'Không phí mở thẻ & phí thường niên';
  static const String cardBenefit_2 = 'Nhận lại 10% hàng tháng lên đến 500K';
  static const String cardBenefit_3 = 'Lãi suất 0%, mua hàng trả trước 0đ';
  static const String cardBenefit_4 = 'Miễn lãi 45 ngày';
  static const String allVoucherTitle = 'Tất cả ưu đãi';
  static const String listVoucherTitle = 'EVO đang có gì?';

  // Profile Page
  static const String myVoucher = 'Ưu đãi của tôi';
  static const String transactionHistory = 'Lịch sử giao dịch';
  static const String emiManagement = 'Quản lý trả góp';
  static const String setting = 'Cài đặt';
  static const String frequentlyQuestions = 'Câu hỏi thường gặp';
  static const String cardUsageGuide = 'Cẩm nang sử dụng thẻ';
  static const String aboutUs = 'Về chúng tôi';
  static const String signOut = 'Đăng xuất';
  static const String version = 'Phiên bản';
  static const String moneyHide = 'xxxxxxđ';
  static const String unKnowUser = 'Người dùng Evo';
  static const String unKnowPhone = '09xxxxxxxx';
  static const String prefixMale = 'Anh';
  static const String prefixFemale = 'Chị';
  static const String unknownGivenName = 'Bạn';
  static const String defaultVersion = 'v1.0.0';
  static const String doneNow = 'Hoàn tất ngay';
  static const String connectEvoCardNow = 'Liên kết thẻ EVO ngay';
  static const String tutorialActiveCard = 'Hướng dẫn kích hoạt';
  static const String cardStatusTitleCreditLimit = 'Hạn mức thẻ';
  static const String cardStatusTitleAwaitingForApproval = 'Hạn mức thẻ chờ phê duyệt';

  // Referral
  static const String referralNewMemberProfile = 'Giới thiệu bạn mới có quà';
  static const String referralIntroductionTitle = 'Chi tiết chương trình';

  /// Number of masked characters for credit limit is defined at https://trustingsocial1.atlassian.net/wiki/spaces/EMA/pages/3335356642/View+credit+limit
  static const String hiddenCreditLimit = '********';
  static const String cardStatusTwoRemainingSteps = 'Chỉ 2 bước nữa để thanh toán';
  static const String cardStatusOneRemainingStep = 'Chỉ 1 bước nữa để thanh toán';
  static const String cardStatusNotLinked = 'Liên kết thẻ';
  static const String cardStatusLinked = 'Đã liên kết';
  static const String cardStatusNotActivated = 'Kích hoạt thẻ';
  static const String cardStatusActivated = 'Đã kích hoạt';
  static const String getCardStatusError = 'Chưa thể cập nhật thông tin của bạn';

  // User Info
  static const String fullName = 'Họ và tên';
  static const String phoneNumber = 'Số điện thoại';
  static const String identityCardNumber = 'Số CMND/CCCD';
  static const String birthday = 'Ngày sinh';
  static const String editInfo = 'Sửa thông tin';
  static const String email = 'Email';
  static const String female = 'Nữ';
  static const String male = 'Nam';

  // Feedback page
  static const String sendEmailTo = 'Hoặc gửi email về';
  static const String feedbackAndContact = 'Góp ý và Liên hệ';
  static const String sendEmail = 'Gửi email';
  static const String feedbackScreenTitle = 'EVO luôn sẵn sàng hỗ trợ bạn';
  static const String feedbackScreenDescription =
      'Mọi góp ý về ứng dụng và thẻ EVO bạn có thể gửi về địa chỉ email ';
  static const String noEmailClientTitle = 'Chưa có ứng dụng email';
  static const String noEmailClientDescription = 'Bạn có thể gửi câu hỏi, góp ý về địa chỉ email ';

  // Profile page
  static const String userInfo = 'Thông tin cá nhân';

  // Profile detail page
  static const String addImage = 'Thêm ảnh';
  static const String deactivateAccount = 'Xoá tài khoản';
  static const String camera = 'Máy ảnh';
  static const String library = 'Thư viện';
  static const String notHaveInfo = 'Chưa có thông tin';

  //Unsupported Phone Number
  static const String createEvoCardTitle = 'Bạn chưa có tài khoản';
  static const String createEvoCardDesc = 'Mở thẻ để có tài khoản dùng mọi dịch vụ trên ứng dụng';
  static const String createEvoCardNow = 'Tiếp tục';

  // No eKYC data for faceOTP
  static const String noEKYCDataForFaceOtpTitle = 'Bạn chưa mở thẻ TPBank EVO thành công';
  static const String noEKYCDataForFaceOtpDesc =
      'Hoàn tất Mở thẻ để có thể sử dụng dịch vụ trên ứng dụng EVO bạn nhé!';

  // Terms & conditions
  static const String termsAndConditions = 'Điều khoản & Điều kiện';

  // Button
  static const String cancel = 'Huỷ bỏ';
  static const String confirm = 'Xác nhận';
  static const String close = 'Đóng';
  static const String viewMore = 'Xem thêm';
  static const String continueBtn = 'Tiếp tục';
  static const String agree = 'Đồng ý';

  // Pin code
  static const String createPin = 'Tạo mã PIN';
  static const String descCreatePin = 'Mã PIN dùng để đăng nhập và xác nhận giao dịch';
  static const String confirmPin = 'Xác nhận mã PIN (6 số)';
  static const String desConfirmPin = 'Nhập lại mã PIN để xác nhận';
  static const String errorConfirmPin = 'Mã PIN chưa khớp, vui lòng kiểm tra và nhập lại.';
  static const String titleCreatePin = 'Nhập mã PIN (6 số)';
  static const String titleCancelInputPin = 'Bạn có chắc muốn thoát?';
  static const String descriptionCancelInputPin =
      'Thông tin bạn vừa nhập sẽ không được lưu lại, bạn cần nhập lại từ đầu.';
  static const String cancelInputPin = 'Thoát';
  static const String continueInputPin = 'Nhập tiếp';

  // Login
  static const String loginScreenTitleWithBiometric = 'Đăng nhập';
  static const String loginScreenTitleWithoutBiometric = 'Nhập mã PIN';
  static const String loginScreenBiometricDescriptionPrefix = 'Đăng nhập tài khoản ';
  static const String loginScreenDescriptionPrefix = 'Nhập mã PIN để đăng nhập tài khoản ';
  static const String loginScreenDescriptionSuffix = ' bạn đã đăng ký tại EVO';
  static const String loginScreenLoginWithAnotherPhoneNumber = 'Đăng nhập bằng số điện thoại khác';
  static const String loginScreenLoginWithBiometric = 'Đăng nhập bằng';
  static const String loginScreenConfirmButton = 'Đăng nhập';
  static const String loginScreenPinCodeOption = 'Hoặc nhập mã PIN để đăng nhập';
  static const String loginScreenResetPin = 'Đặt lại mã PIN';
  static const String loginScreenBioFailMaxTime =
      'Bạn đã đăng nhập sai quá {0} lần bằng {1}. Vui lòng nhập PIN để tiếp tục';
  static const String loginScreenBioLockedAndEnterPin = 'Vui lòng nhập mã PIN để đăng nhập';

  // Session timeout
  static const String authorizationSessionTimeoutTitle = 'Phiên đăng nhập hết hạn';
  static const String authorizationSessionTimeoutDesc =
      'Vui lòng đăng nhập lại để tiếp tục sử dụng các tính năng dành cho thành viên bạn nhé.';
  static const String authorizationSessionTimeoutConfirmLogin = 'Đăng nhập';
  static const String authorizationSessionTimeoutIgnoreLogin = 'Bỏ qua';
  static const String localizedReasonForUsingBiometrics = 'EVO';
  static const String termAndConditionTitle = 'Điều khoản & Điều kiện';
  static const String enterPinTitle = 'Nhập PIN';
  static const String enterPinToAuthenticate = 'Hãy nhập mã PIN để xác thực';
  static const String settingTitle = 'Cài đặt';
  static const String ignoreTitle = 'Bỏ qua';

  //bio metrics error;
  static const String passCodeNotSetError = 'pass code not set';
  static const String notEnrolledError = 'not enrolled biometrics';
  static const String notAvailableError = 'not available biometrics';
  static const String otherOperatingSystemError = 'other operating system error';
  static const String lockedOutError = 'biometrics authentication is locked out';
  static const String permanentlyLockedOutError =
      'biometrics authentication permanently locked out';
  static const String biometricOnlyNotSupportedError =
      'biometrics authentication biometric only not supported';

  static const String otpTitle = 'Nhập mã OTP';
  static const String limitOtp = 'Đăng nhập tạm khóa';
  static const String descLimitOtp = 'Bạn đã đạt giới hạn số lần nhập mã xác thực';
  static const String moveToHome = 'Về trang chủ';

  // Active biometric
  static const String loginSuccess = 'Đăng nhập thành công';
  static const String descSetUpPinSuccess = 'Từ lần sau bạn chỉ cần nhập mã PIN để đăng nhập EVO';
  static const String titlePreFixActiveBiometric = 'Kích hoạt ';
  static const String titleSubFixActiveBiometric =
      ' để xác nhận thanh toán và đăng nhập nhanh thay cho mã PIN';
  static const String textPositiveActiveBiometric = 'Kích hoạt';
  static const String descriptionActiveBiometric =
      'Dùng {0} để đăng nhập và xác thực thanh toán nhanh và bảo mật hơn';

  //Text for No Setup FaceId/FingerId Popup
  static const String noSetupFaceFingerIdDescPart1 = 'Cài đặt {0} trong ';
  static const String noSetupFaceFingerIdDescPart2 =
      ' của thiết bị để xác nhận giao dịch thanh toán và đăng nhập nhanh thay cho mật khẩu.';
  static const String fail = 'Thất bại';
  static const String signOutConfirm = 'Bạn có chắc chắn muốn đăng xuất khỏi ứng dụng EVO?';
  static const String signOutFail = 'Bạn vừa đăng xuất không thành công';
  static const String signOutSuccess = 'Bạn đã đăng xuất thành công';
  static const String faceText = 'khuôn mặt';
  static const String fingerText = 'vân tay';
  static const String faceFingerText = 'khuôn mặt/ vân tay';
  static const String authenticateText = 'Xác thực';
  static const String notSetupText = 'Chưa cài đặt';
  static const String enableText = 'Kích hoạt';
  static const String prefixDisableBioAuthenticatorText = 'Đã tắt xác thực';
  static const String biometricTokenUnUsableMessage =
      'Đăng nhập bằng {0} đã hết hạn. Vui lòng kích hoạt lại trong Cài đặt của ứng dụng';
  static const String titleActiveBiometric = 'Xác thực bằng {0}?';
  static const String biometricLocked = '{0} của bạn đã bị khóa. Vui lòng vào Cài Đặt để mở khóa';

  //Setting pages
  static const String resetPin = 'Đặt lại mã PIN';
  static const String deleteAccountPrompt = 'Nếu bạn muốn xoá tài khoản EVO, hãy';
  static const String deleteAccountActionText = 'bấm vào đây';

  // Promotion pages
  static const String campaignListPageTitle = 'Ưu đãi';
  static const String promotionListPageTitle = 'Ưu đãi';
  static const String allCampaigns = 'Tất cả ưu đãi';
  static const String emptyCampaignText = 'Chưa có ưu đãi nào';
  static const String emptyCampaignDescription =
      'Cùng chờ đón các chương trình ưu đãi hấp dẫn của EVO trong thời gian tới nhé';
  static const String emptyMyPromotionText = 'Ưu đãi của bạn sẽ hiện ở đây';
  static const String emptyMyPromotionDescription =
      'Tham gia các chương trình ưu đãi của EVO để nhận thêm nhiều ưu đãi nhé';
  static const String promotionRunningOut = 'Còn';
  static const String promotionDay = 'ngày';
  static const String promotionHour = 'giờ';
  static const String promotionMinute = 'phút';
  static const String promotionUse = 'Sử dụng';
  static const String promotionIsUsed = 'Đã sử dụng';
  static const String voucherEndAt = 'HSD';
  static const String campaignEndAt = 'Kết thúc vào';
  static const String promotionStartAt = 'Bắt đầu vào';
  static const String promotionTimeOut = 'Đã hết hạn';
  static const String campaignTimeOut = 'Đã kết thúc';

// Announcement
  static const String announcementListTitle = 'Thông báo';
  static const String transactionTitle = 'Giao dịch';
  static const String todayText = 'Hôm nay';
  static const String yesterdayText = 'Hôm qua';
  static const String noAnnouncementDescription = 'Bạn chưa có thông báo';
  static const String announcementDetailHeaderTitle = 'Nội dung chi tiết';

  // Transaction
  static const String statusSuccess = 'Thành công';
  static const String statusPending = 'Chờ thanh toán';
  static const String statusFailed = 'Chưa thành công';
  static const String tpBankEvo = 'TPBank EVO';
  static const String transactionHistoryEmiLabel = 'Trả góp';
  static const String refundInfoTitle = 'Hoàn tiền EVO';
  static const String refundInfoDescription =
      'Khoản tiền hoàn này sẽ được cộng vào hạn mức tín dụng của Khách hàng';
  static const String refundInfoTimeDescription =
      'Thời gian hoàn tiền chậm nhất là ngày 20 trong tháng kế tiếp của chu kỳ sao kê có chứa giao dịch gốc';

  //Web view
  static const String webViewErrorTitle = 'Không tìm thấy trang';
  static const String webViewErrorDescription =
      'Trang của bạn không tồn tại hoặc\nđang có lỗi xảy ra';

  // Promotion detail
  static const String copyPromotionCodeSuccess = 'Đã sao chép mã ưu đãi vào bộ nhớ';
  static const String promotionCopyAction = 'Sao chép và sử dụng ngay';
  static const String copyPromotionUsingAction = 'Sử dụng mã ưu đãi';
  static const String campaignDefaultAction = 'Tham gia ngay';
  static const String promotionDetailTitle = 'Chi tiết ưu đãi';
  static const String campaignDetailTitle = 'Chi tiết chương trình';
  static const String voucherEarningSuccessCtaText = 'Xem ưu đãi ngay';
  static const String voucherEarningErrorTitle =
      'Rất tiếc, hệ thống đang gián đoạn.\nBạn vui lòng thử lại sau nhé!';

  static const String biometricDeviceChangeWarring =
      '{0} đã bị thay đổi. Vui lòng kiểm tra lại thiết lập {1} trong máy';

  /// Payment flow
  static const String paymentTitle = 'Thanh toán';
  static const String paymentAmountHint = 'Nhập số tiền';
  static const String paymentNote = 'Ghi chú';
  static const String paymentDescPrefix = 'Thanh toán cho';
  static const String paymentAmountInvalid = 'Số tiền không hợp lệ';
  static const String paymentAmountMinInvalid = 'Bạn cần nhập tối thiểu';
  static const String paymentAmountMaxInvalid = 'Bạn chỉ có thể nhập tối đa';
  static const String paymentContinue = 'Tiếp tục';
  static const String paymentConfirmTitle = 'Xác nhận thanh toán';
  static const String paymentSummaryTransactionCode = 'Mã giao dịch';
  static const String paymentSummaryDesc = 'Mô tả';
  static const String paymentSummaryDateTime = 'Thời gian';
  static const String paymentSummaryAmount = 'Số tiền';
  static const String paymentSummaryFee = 'Phí giao dịch';
  static const String paymentSummaryPromotionAmount = 'Ưu đãi';
  static const String paymentSummaryCashbackAmount = 'Hoàn tiền';
  static const String paymentSummaryTotalAmount = 'Tổng thanh toán';
  static const String paymentSummaryPayment = 'Thanh toán';
  static const String paymentEmiSummaryPayment = 'Thanh toán trả góp';
  static const String payment3DSTitle = 'Xác minh thẻ';
  static const String paymentBackToHome = 'Về trang chủ';
  static const String paymentRePayment = 'Thanh toán lại';
  static const String paymentPollingTimeoutTitle = 'Để bạn đợi lâu thiệt là có lỗi';
  static const String paymentPollingTimeoutDesc =
      'Thanh toán lại giúp EVO nhé, lần này EVO sẽ cố gắng xử lý nhanh hơn 🚀';
  static const String paymentResultSuccessFullName = 'Giao dịch thành công';
  static const String paymentResultFailFullName = 'Giao dịch thất bại';
  static const String paymentResultPendingFullName = 'Giao dịch đang chờ xử lý';
  static const String paymentSelectPromotion = 'Chọn ưu đãi';
  static const String paymentPromotionEmptyTitle = 'Chưa có ưu đãi phù hợp';
  static const String paymentPromotionEmptyDesc =
      'Chưa có ưu đãi nào phù hợp với giao dịch này, cùng đợi thêm các ưu đãi khác từ EVO nhé';
  static const String paymentSelectPromotionTitle = 'Danh sách ưu đãi';
  static const String paymentSelectPromotionButton = 'Áp dụng';
  static const String paymentUnSelectPromotionButton = 'Bỏ chọn';
  static const String paymentPromotionNotQualified = 'Không đủ điều kiện';
  static const String paymentPromotionCanNotSelected = 'Chỉ được chọn 1 ưu đãi';
  static const String paymentPromotionUnavailable =
      'Ưu đãi bạn đang chọn không còn sử dụng được, chọn ưu đãi khác nhé';
  static const String paymentPromotionApplyTimeout = 'Bạn chưa áp dụng ưu đãi';
  static const String paymentPromotionRemoveTimeout = 'Ưu đãi chưa được bỏ chọn, thử lại bạn nhé';
  static const String paymentPromotionTimeout = 'Bạn chưa áp dụng ưu đãi';
  static const String paymentPromotionApplyErrorTitle = 'Chưa thể áp dụng ưu đãi';
  static const String paymentPromotionApplyErrorSelectOtherPromotion = 'Chọn ưu đãi khác';
  static const String paymentPromotionApplyErrorPayWithoutPromotion =
      'Thanh toán không dùng ưu đãi';
  static const String paymentWithNoPromotionWarning = 'Áp dụng ưu đãi để thanh toán tiết kiệm hơn';
  static const String poweredByLabel = 'Powered by';
  static const String payment3DSConfirmDialogTitle = 'Bạn có chắc muốn quay lại?';
  static const String payment3DSConfirmDialogContent =
      'Nếu bạn đã nhập mã OTP, giao dịch này có thể vẫn đang được xử lý. Kết quả giao dịch sẽ được cập nhật sau 15 phút.';
  static const String payment3DSKeepWaiting = 'Tiếp tục đợi';
  static const String payment3DSBackScreen = 'Quay lại';
  static const String updateNewOrderWithInvalidVoucher = 'Ưu đãi chưa thể áp dụng được';
  static const String promotionItemUnqualified = '(Chưa thể áp dụng ưu đãi)';

  /// The default error message for this bottom sheet is defined at: https://trustingsocial1.atlassian.net/wiki/spaces/EMA/pages/3348595023/Pre-select+Vouchers+at+Checkout#ii.-Proceeding-with-payment
  static const String invalidPromotionBottomSheetDefaultError =
      'Bạn không thể sử dụng ưu đãi này, kiểm tra và chọn ưu đãi khác nhé';
  static const String transactionTooSoonBottomSheetTitle = 'Giao dịch trước đang được xử lý';
  static const String transactionTooSoonBottomSheetDescription =
      'Giao dịch trước đó của bạn đang được xử lý, vui lòng chờ ít nhất 1 phút trước khi thanh toán lại nhé';

  // QR code screen
  static const String scanQRCodeGuideline = 'Hướng khung Camera\nvào vị trí có mã QR';
  static const String scanQRCodeTitle = 'Quét mã';
  static const String cameraPermissionTitle = 'Quyền truy cập Camera';
  static const String cameraPermissionDescription =
      'Kích hoạt Camera trong phần cài đặt để thực hiện tính năng scan QR';
  static const String errorQrCodeDefaultErrorDescription =
      'EVO không thể quét mã QR này, bạn kiểm tra và thử lại nhé';
  static const String scanQRClose = 'Đóng';
  static const String scanQRRetry = 'Thử lại';
  static const String storeNotFoundDefaultError = 'Cửa hàng không tồn tại';
  static const String userCardNotSupportTitle = 'Chưa hỗ trợ với thẻ của bạn';
  static const String userCardNotSupportContent =
      'EVO sẽ gửi thông báo cho bạn ngay khi thẻ được hỗ trợ, bạn cố đợi thêm nhé';
  static const String scanFailBackToHome = 'Về trang chủ';
  static const String cantCheckLinkedCardTitle = 'Ơ, có chút vấn đề rồi';
  static const String cantCheckLinkedCardContent =
      'EVO đang kiểm tra và khắc phục, bạn đợi một chút rồi thử lại nhé.';
  static const String cantCheckLinkedCardButton = 'Về trang chủ';
  static const String defaultCantLinkedCartStatusContent =
      'Chưa đủ điều kiện liên kết thẻ. Thử lại sau bạn nhé.';
  static const String titleErrorPopupInvalidFormat = 'Mã QR không hợp lệ';
  static const String titleErrorPopupOrderInvalid = 'Đơn hàng không hợp lệ';
  static const String titleErrorPopupOrderExpired = 'Đơn hàng đã hết hạn';
  static const String titleErrorPopupOrderInPayment = 'Đơn hàng đang được xử lý';
  static const String titleErrorPopupInvalidOrderStatus = 'Đơn hàng đang xử lý hoặc đã thất bại';
  static const String titleErrorPopupPayTimesExceeded = 'Không thể thanh toán đơn hàng này';
  static const String titleErrorPopupOrderSucceeded = 'Đơn hàng đã được thanh toán';
  static const String titleErrorPopupOrderFailed = 'Đơn hàng đã thất bại';
  static const String titleErrorPopupOrderCancelled = 'Đơn hàng đã bị hủy';
  static const String titleErrorPopupOrderPending = 'Đơn hàng đang được xử lý';
  static const String descriptionEmiNotSupportForThisProduct =
      'EVO hiện chưa hỗ trợ trả góp với phương thức thanh toán này';
  static const String descriptionEmiNotSupportForVNPay =
      'EVO hiện chưa hỗ trợ trả góp với phương thức thanh toán VNPay';

  //Home user menu
  static const String menuUserGuideCardUsage = 'Hướng dẫn\nsử dụng thẻ';
  static const String menuUserReward = 'Ưu đãi\ncủa tôi';
  static const String menuUserScanner = 'Quét mã\nthanh toán';
  static const String menuUserEmiPayment = 'Trả góp\nlinh hoạt';
  static const String menuUserQuestion = 'Câu hỏi\nthường gặp';

  // Account deleting flow
  static const String confirmDeactivateTitle = 'Khi xoá tài khoản, bạn sẽ:';
  static const String confirmDeactivateNotice = '  \u2022  Huỷ liên kết thẻ trên ứng dụng EVO\n'
      '  \u2022  Không thể quét mã thanh toán và dùng ưu đãi trên EVO\n'
      '  \u2022  Không thể dùng số điện thoại này để tạo tài khoản EVO trong 30 ngày';
  static const String confirmDeactivatePositiveButton = 'Xoá tài khoản';
  static const String confirmDeactivateNegativeButton = 'Không xoá';
  static const String deactivateAccountFailTitle = 'Chưa thể xoá tài khoản EVO';
  static const String deactivateAccountFailNotice =
      'Lâu rồi chưa gặp trường hợp nào như thế này, bạn thử lại lần nữa nhé';
  static const String deactivateAccountSuccessTitle = 'Đã xoá tài khoản EVO';
  static const String deactivateAccountSuccessNotice =
      '  \u2022  Các giao dịch trên thẻ EVO bạn có thể theo dõi trên ứng dụng TPBank\n'
      '  \u2022  Bạn có thể đăng ký lại tài khoản EVO sau 30 ngày nữa. EVO đợi bạn 👋🏻';

  // Transaction history
  static const String transactionHistoryEmptyTittle = 'Chưa có giao dịch nào thanh toán bằng EVO';
  static const String transactionHistoryEmptyDesc =
      'Mọi giao dịch thanh toán bằng ứng dụng EVO của bạn sẽ hiện ở đây';
  static const String transactionHistoryNotLogin = 'Đăng nhập để xem giao dịch';
  static const String transactionHistoryNotLoginDesc =
      'Mọi giao dịch thanh toán bằng ứng dụng EVO của bạn sẽ hiện ở đây';
  static const String transactionHistoryProcessing = 'Đang xử lý';
  static const String transactionHistorySuccess = 'Thành công';
  static const String transactionHistoryFailure = 'Thất bại';
  static const String transactionHistoryTitle = 'Chi tiết giao dịch';
  static const String transactionHistoryCashbackTitle = 'Ưu đãi hoàn tiền đã nhận';
  static const String transactionHistoryCashbackPeriodTitle = 'Kỳ tháng {0}';
  static const String transactionHistoryCashbackBottomSheetDesc =
      'Khoản tiền hoàn sẽ được ghi có vào tài khoản thẻ tín dụng chậm nhất vào ngày 20 của tháng kế tiếp kỳ sao kê có phát sinh giao dịch.';
  static const String transactionHistoryCashbackNameBottomSheetTitle = 'Ứng dụng EVO';

  // Transaction detail
  static const String transactionDetailTitleProcessing = 'Đang xử lý thanh toán tại {0}';
  static const String transactionDetailOutrightPurchaseTitleSuccess = 'Bạn đã thanh toán tại {0}';
  static const String transactionDetailEmiTitleSuccess = 'Đã ghi nhận đăng ký trả góp tại {0}';
  static const String transactionDetailTitleErrorPreFix = 'Giao dịch thất bại tại {0}';

  // Linked card
  static const String linkedCardDetailTitle = 'Tài khoản liên kết';
  static const String linkedCardLimitation = 'Hạn mức thanh toán';
  static const String linkedCardMinimumLimitation = 'Hạn mức tối thiểu';
  static const String linkedCardMaximumLimitation = 'Hạn mức tối đa';
  static const String linkedCardLimitPerTrans = ' / Giao dịch';

  // Intro
  static const String introductionText1 = 'Quét mã thanh toán không cần thẻ cứng';
  static const String introductionText2 = 'Mở thẻ online 24/7 chỉ trong 10 phút';
  static const String introductionText3 = 'Nhiều ưu đãi hấp dẫn từ hàng ngàn đối tác EVO';
  static const String introductionSkip = 'Bỏ qua';

  // Non-authorized user with scan function
  static const String nonAuthorizedUserScanPopupTitle = 'Đăng nhập để sử dụng EVO';
  static const String nonAuthorizedUserScanPopupContent =
      'Quét mã thanh toán không cần thẻ cứng, quản lý chi tiêu hiệu quả ngay trên ứng dụng';
  static const String nonAuthorizedUserScanPopupButton = 'Đăng nhập';

  //force update
  static const String forceUpdateTitle = 'Thông báo';
  static const String forceUpdateDescription =
      'Cập nhật ngay phiên bản mới để có trải nghiệm mượt mà hơn & sử dụng các tính năng mới nhất.';
  static const String forceUpdateSubDesc = 'Đã có phiên bản mới';
  static const String forceUpdateAgree = 'Cập nhật';
  static const String forceUpdateSkip = 'Bỏ qua';

  //Reset Pin
  static const String resetPinSuccess = 'Đã đặt mã PIN thành công';
  static const String titleNationalId = 'Nhập số CCCD/CMND';
  static const String descriptionNationalId = 'Số bạn đã dùng để đăng ký thẻ EVO';
  static const String hintNationalId = 'Nhập ở đây nhé';
  static const String titleLimitResetPin = 'Tạm khoá Đặt lại mã PIN';
  static const String descriptionLimitResetPin =
      'Số CCCD/CMND sai quá số lần giới hạn, thử lại sau 24 giờ nữa bạn nhé';
  static const String errorLimitResetPin =
      'Mã OTP sai quá số lần giới hạn, thử lại sau 24 giờ nữa bạn nhé';
  static const String titleSessionTokenExpiredSignIn = 'Hết thời gian đăng nhập';
  static const String contentSessionTokenExpiredSignIn =
      'Đăng nhập lại để tiếp tục sử dụng EVO nhé';
  static const String textSubmitSessionTokenExpiredSignIn = 'Đăng nhập lại';
  static const String titleSessionTokenExpiredResetPin = 'Hết thời gian đổi mã PIN';
  static const String contentSessionTokenExpiredResetPin =
      'Hãy quay lại trang trước và bấm đặt lại mã PIN bạn nhé';
  static const String textSubmitSessionTokenExpiredResetPin = 'Quay lại';

  // DOP
  static const String dopCardStatusDefaultTitle = 'Chưa thể liên kết thẻ';
  static const String dopDuplicatedLinkRequestTitle = 'Bạn có một yêu cầu liên kết thẻ đang xử lý';
  static const String dopUnqualifiedUserInformationTitle = 'Liên kết thẻ thất bại';
  static const String dopUnqualifiedCardTitle = 'Chưa thể cài đặt phương thức thanh toán';
  static const String dopUnfulfilledCardDescription =
      'Bạn cần hoàn tất mở thẻ EVO trước khi thực hiện liên kết thẻ';
  static const String dopWaitingForIssuingCardDescription =
      'Hồ sơ mở thẻ đang được duyệt. Bạn có thể liên kết sau khi nhận thông báo mở thẻ thành công.';
  static const String dopUnqualifiedCardDescription =
      'Bạn đã huỷ đăng ký mở thẻ hoặc hồ sơ mở thẻ của bạn không được duyệt';
  static const String dopDuplicatedLinkRequestDescription =
      'EVO sẽ thông báo cho bạn khi có kết quả liên kết';
  static const String dopUnqualifiedUserInformationDescription =
      'Chưa đủ điều kiện liên kết thẻ. Thử lại sau bạn nhé.';
  static const String dopContinueManualLinkCard = 'Tiếp tục mở thẻ';

  /// manual link card result
  static const String resultSuccessDescription = 'Thanh toán nhanh hơn khi quét mã QR trên EVO';
  static const String resultWaitingForLinkingCardDescription =
      'EVO sẽ thông báo cho bạn khi có kết quả liên kết';
  static const String resultLinkingCardDefaultDescription =
      'EVO chưa thể xử lý yêu cầu liên kết thẻ của bạn. Thử lại sau {0} phút nữa nhé.';
  static const String resultClose = 'Đóng';
  static const String resultReLinkCard = 'Liên kết lại';
  static const String resultSuccessTitle = 'Liên kết thẻ thành công';
  static const String resultProcessingTitle = 'Đang liên kết thẻ';
  static const String resultBackToCheckout = 'Trở về trang thanh toán';

  // manual link card
  static const String titleManualLinkCardCheckOut = 'Liên kết thẻ để thanh toán';
  static const String descriptionManualLinkCardCheckOut =
      'Liên kết thẻ TPBank EVO để thanh toán với các ưu đãi cực hấp dẫn từ EVO và đối tác';
  static const String manualLinkCardButtonTitleCheckOut = 'Liên kết ngay';
  static const String titleManualLinkCardScanQr = 'Chưa cài đặt phương thức thanh toán';
  static const String descriptionManualLinkCardScanQr =
      'Bạn cần liên kết thẻ với ứng dụng EVO để thanh toán trên ứng dụng';
  static const String manualLinkCardButtonTitleScanQr = 'Cài đặt ngay';
  static const String manualLinkCard3DSTitle = 'Xác minh thẻ';
  static const String linkCardCancellationWarningContent =
      'Nếu rời màn hình này, bạn cần đợi thêm {0} phút để có thể liên kết lại.';
  static const String linkCardCancellationWarningTitle = 'Bạn có chắc muốn thoát?';
  static const String linkCardKeepCancellation = 'Ở lại';
  static const String linkCardDiscardCancellation = 'Thoát';
  static const String linkCardInProcessing = 'Đang liên kết thẻ';
  static const String linkCardProcessingCanTakeSomeMinutes =
      'Quá trình liên kết có thể mất vài phút. Bạn đừng tắt ứng dụng nhé.';
  static const String linkCardOtpTimeout =
      'Đã hết thời gian liên kết. Bạn có thể thử lại sau {0} phút nữa nhé ⏰';
  static const String linkCardPollingTimeout = 'EVO sẽ thông báo cho bạn khi có kết quả liên kết';
  static const String submitLinkCardWithPermissionDenied =
      'EVO chưa thể xử lý yêu cầu liên kết thẻ của bạn. Thử lại sau bạn nhé.';
  static const String submitLinkCardWithRecordNotFoundAndFailed =
      'EVO chưa thể xử lý yêu cầu liên kết thẻ của bạn. Thử liên kết lại bạn nhé 😉';
  static const String submitLinkCardWithDefaultDescription =
      'EVO chưa thể xử lý yêu cầu liên kết thẻ của bạn. Thử lại sau {0} phút nữa nhé.';

  // Detect Root/Jailbreak
  static const String titleBlockInsecureDeviceDialog = 'EVO chưa hỗ trợ thiết bị này';
  static const String descriptionBlockInsecureDeviceDialog =
      'EVO chưa hỗ trợ các thiết bị đã bẻ khoá để bảo đảm an toàn cho tài khoản của bạn';

  // FaceOTP
  static const String noticeFaceOtpTitle = 'Các lưu ý khi chụp';
  static const String verifyFaceOtpTitle = 'Bạn cần xác thực khuôn mặt';
  static const String verifyFaceOtpSignInDescription =
      'Xác thực danh tính của bạn khi đăng nhập và sử dụng ứng dụng';
  static const String verifyFaceOtpLinkCardDescription =
      'Xác thực danh tính của bạn để liên kết thẻ TPBank EVO';
  static const String instructFaceOTPInFrame = 'Luôn giữ đầu trong khung hình';
  static const String instructFaceOTPNoGlasses =
      'Không đeo kính râm, nón hoặc các phụ kiện che mặt';
  static const String instructFaceOTPCleanEnvironment =
      'Môi trường chụp không quá tối hoặc chói sáng';
  static const String startFaceOTP = 'Bắt đầu chụp';
  static const String cameraFaceOtpPermissionDescription =
      'Kích hoạt Camera trong phần cài đặt để thực hiện tính năng xác thực khuôn mặt';
  static const String faceOtpExceedLimitErrorTitle = 'Chưa thể xác thực khuôn mặt';
  static const String faceOtpExceedLimitationDescription =
      'Bạn đã xác thực khuôn mặt quá số lần giới hạn. Thử lại sau nhé.';
  static const String faceOtpBackButtonTitle = 'Quay lại';
  static const String faceOtpSuccess = 'Xác thực khuôn mặt thành công';
  static const String faceOtpMatchingErrorTitle = 'Xác thực thất bại';
  static const String faceOtpMatchingErrorDescription =
      'Bạn không được dùng ảnh có sẵn để xác thực khuôn mặt';
  static const String faceOtpMatchingRetryTitle = 'Thử lại';
  static const String faceOtpMatchingProcessing = 'Hệ thống đang xử lý,\n xin chờ trong giây lát';
  static const String eKYCLoadingTitle = 'Bạn đợi EVO chút nhé';
  static const String eKYCLoadingContent =
      'Tháo mũ, kính râm, chọn nơi đủ sáng để xác thực nhanh và chính xác';
  static const String eKYCErrorTitle = 'Ơ sao lại thế nhỉ';
  static const String eKYCSelfieCapturingErrorDescription =
      'EVO chưa thể xác thực khuôn mặt của bạn, thử lại lần nữa nhé';
  static const String eKYCSelfieCapturingCannotRetryErrorDescription =
      'EVO chưa thể xác thực ảnh khuôn mặt của bạn';

  static const String faceOtpSessionExpireTitle = 'Phiên làm việc hết hạn';
  static const String faceOtpSessionExpireDescription =
      'Phiên làm việc của bạn đã hết hạn, vui lòng nhấn vào nút bên dưới để tiếp tục';

  // Private policy
  static const String privatePolicy = 'Chính sách xử lý dữ liệu';
  static const String policyRequestAcceptTitle = 'Bạn cần đồng ý với bản cập nhật ngày {0}';
  static const String policyAcceptedTitle = 'Cập nhật ngày {0}';
  static const String policyDialogTitle = 'Bạn cần đồng ý để tiếp tục sử dụng EVO';
  static const String policyDialogContent =
      '{0} nhằm bảo vệ thông tin, dữ liệu do bạn cung cấp và để tối ưu trải nghiệm cho bạn khi sử dụng EVO';
  static const String policyDialogNegative = 'Xem lại';
  static const String policyAccept = 'Tôi đồng ý';
  static String downloadConsentSuccess = 'Tải xuống thành công';
  static String downloadConsentError = 'Tải xuống không thành công';
  static const String storagePermissionTitle = 'Chưa thiết lập lưu trữ tập tin';
  static const String storagePermissionDescription =
      'Hãy đồng ý cho EVO lưu trữ tập tin trong Cài đặt của thiết bị để tải tập tin này';
  static const String errorLoadingPdf = 'Không tải được nội dung';
  static const String privatePolicyFileName = 'Chinh sach xu ly du lieu EVO';

  // Pre Face Otp of Manual link card flow
  static const String preFaceOtpTitleManualLinkCard = 'Xác thực khuôn mặt để liên kết';
  static const String preFaceOtpButtonTitleManualLinkCard = 'Tiếp tục ({0}s)';
  static const String preFaceOtpDescriptionManualLinkCard =
      'Chỉ 1 bước chụp ảnh khuôn mặt nữa để liên kết thẻ với ứng dụng EVO 📸';

  // Delete Account Flow
  static const String attentionNotesTitle = 'Vài điều bạn cần lưu ý';
  static const String attentionNoteContents =
      '\nBạn có thể khôi phục tài khoản trong vòng 30 ngày chỉ với việc đăng nhập EVO bằng số điện thoại này.'
      '\n\nBạn có thể tiếp tục theo dõi các giao dịch thẻ EVO trên ứng dụng TPBank Mobile.'
      '\n\nNếu có ghi nhận một yêu cầu đăng ký thẻ tín dụng TPBank EVO bằng số điện thoại này, thì tài khoản sẽ bị xoá ngay lập tức để bảo vệ an toàn thông tin cá nhân và thông tin thẻ nếu có.'
      '\n\nTài khoản sẽ bị xoá vĩnh viễn khi quá 30 ngày kể từ thời điểm đề nghị xoá tài khoản.'
      '\n\nSau khi tài khoản bị xoá vĩnh viễn:\n';
  static const String attentionNoteAfterDeleteUnlinkEvoCard =
      'Thẻ của bạn sẽ bị huỷ liên kết với ứng dụng EVO';
  static const String attentionNoteAfterDeleteCantUseEvoApp =
      'Bạn không thể dùng EVO để quét mã thanh toán và dùng ưu đãi';
  static const String attentionNoteCarefullyConsider =
      '\nCân nhắc kỹ trước khi xoá tài khoản bạn nhé. EVO có rất nhiều tiện ích và ưu đãi hấp dẫn dành riêng cho bạn 🎁';
  static const String attentionNotesLearnMore = 'Tìm hiểu thêm về EVO';
  static const String attentionNotesCancelDelete = 'Không xoá';
  static const String attentionNotesConfirmDelete = 'Xoá tài khoản';
  static const String attentionNotesProcessing = 'Đang xử lý...';

  static const String lockAccountToDeleteTitle = 'Tạm khoá Xoá tài khoản';
  static const String lockAccountToDeleteContent =
      'Chưa hết 24 giờ tạm khoá tính năng Xoá tài khoản, bạn đợi thêm chút nữa nhé';

  static const String confirmToDeleteAccountTitle = 'Bạn muốn xoá tài khoản?';
  static const String confirmToDeleteAccountDescription =
      'Bạn sẽ không thể dùng tài khoản này để quét mã QR thanh toán và sử dụng ưu đãi trên ứng dụng EVO';

  static const String cancelToDeleteAccountTextBtn = 'Không xoá';
  static const String deleteAccountTextBtn = 'Xoá tài khoản';

  static const String deleteAccountSuccessTitle = 'Xoá tài khoản thành công';
  static const String deleteAccountSuccessDescription =
      'Bạn có thể khôi phục tài khoản bằng cách đăng nhập lại EVO trong vòng 30 ngày';

  static const String confirmPinToDeleteAccountText = 'Xác nhận mã PIN';
  static const String confirmPinToDeleteAccountDescription =
      'Nhập mã PIN để xác nhận xoá tài khoản';
  static const String lockAccountDueToVerifyPinLimitExceedDescription =
      'Mã PIN sai quá số lần giới hạn, thử lại sau 24 giờ nữa bạn nhé';
  static const String deleteAccountSurveyTitle = 'Chia sẻ lý do của bạn nhé';
  static const String deleteAccountSurveyDescription =
      'Điều này giúp EVO tốt hơn trong thời gian tới';
  static const String deleteAccountSurveyFooter = 'EVO sẵn sàng lắng nghe bạn. ';
  static const String deleteAccountContactSurveyEvo = 'Liên hệ EVO';
  static const String deleteAccountRemindUserSelectReason =
      'Cho EVO biết lý do xoá tài khoản của bạn nhé';
  static const String welcomeBackDialogTitle = 'Chào mừng bạn trở lại';
  static const String welcomeBackDialogContent = 'Bạn đã khôi phục tài khoản thành công';
  static const String welcomeBackDialogBtn = 'Đóng';
  static const String deleteAccountSessionExpiredTitle = 'Yêu cầu hết hiệu lực';
  static const String deleteAccountSessionExpiredContent =
      'Yêu cầu xoá tài khoản của bạn đã hết hiệu lực, thực hiện lại lần nữa nhé';
  static const String deleteAccountSessionExpiredButton = 'Đóng';

  // referral feature
  static const String sharingText = 'Chia sẻ';
  static const String referralQrCodeTitle = 'Mã của bạn nè!';
  static const String referralQrCodeDescription =
      'Đưa mã hoặc link này cho bạn bè mở thẻ EVO để nhận quà';
  static const String referralLinkLoadingTitle = 'Bạn đợi EVO chút nhé';
  static const String referralLinkLoadingDescription = 'EVO đang kiểm tra thông tin chương trình';
  static const String referralLinkLoadingUserNotInCampaignTitle = 'Chương trình chưa phù hợp';
  static const String referralLinkLoadingUserNotInCampaignDescription =
      'Cùng chờ đón các chương trình ưu đãi hấp dẫn từ EVO trong thời gian tới bạn nhé';
  static const String referralLinkGetApiErrorTitle = 'Chưa thể tải thông tin chương trình';
  static const String referralLinkGetApiErrorDescription =
      'Quay lại để lấy lại link nhé, chương trình rất hấp dẫn đang đợi bạn';
  static const String referralLinkBackBtn = 'Quay lại';
  static const String referralLinkConfirmCopy = 'Đã sao chép';

  // Download File
  static const String downloadLinkFileSuccess = 'Tải file thành công';
  static const String downloadLinkFileFail = 'Tải file không thành công';
  static const String startDownloadLinkFile = 'Đang tải file, bạn đợi chút nhé';

  // Payment Input Amount V2
  static const String fullPaymentInputAmountTitle = 'Bạn đang thanh toán tại';
  static const String fullPaymentInputAmountUpdateNote = 'Thêm ghi chú';
  static const String inputAmountUpdateNoteSuccess = 'Đã thêm ghi chú';
  static const String inputAmountEmpty = 'Nhập số tiền cần thanh toán ở đây bạn nhé';
  static const String paymentNotePrefix = 'Tôi muốn thanh toán số tiền này tại';
  static const String paymentAmountV2Hint = 'nhập số tiền';

  // EMI
  static const String suffixTitleEmi = 'tháng';
  static const String payWithEMITitle = 'Bạn có thể trả góp tại đơn hàng này';
  static const String payWithEMIDescription = 'Chia nhỏ số tiền trả mỗi tháng, lãi suất 0%';
  static const String titlePayInAll = 'Chuyển sang trả thẳng';
  static const String emiUnqualifiedCancelButton = 'Huỷ';
  static const String titleChooseOtherPromotion = 'Chọn ưu đãi khác';
  static const String titleEmiWithoutPromotion = 'Trả góp không dùng ưu đãi';

  ///EMI payment result
  static const String paymentResultEmiTitleProcess = 'Đang xử lý giao dịch tại {0}';
  static const String paymentResultOutrightOPurchaseTitleSuccess = 'Bạn đã thanh toán tại {0}';
  static const String paymentResultEmiTitleSuccess = 'Đã ghi nhận đăng ký trả góp tại {0}';
  static const String paymentResultEmiTitleError = 'Giao dịch thất bại tại {0}';
  static const String paymentResultEmiDetail = 'Chi tiết';
  static const String paymentResultEmiTransactionCode = 'Mã giao dịch';
  static const String paymentPartnerReferenceLabel = 'Mã tham chiếu';
  static const String paymentPartnerOrderId = 'Mã đơn hàng';
  static const String paymentResultEmiTime = 'Thời gian';
  static const String paymentResultEmiDescription = 'Mô tả';
  static const String paymentResultEmiTenor = 'Kỳ hạn trả góp';
  static const String paymentResultEmiMonthlyPay = 'Trả mỗi tháng';
  static const String paymentResultEmiConversionFee = 'Phí chuyển đổi';
  static const String paymentResultEmiRate = 'Lãi suất';
  static const String paymentResultEmiVoucher = 'Ưu đãi';
  static const String paymentResultEmiFee = 'Phí giao dịch';
  static const String paymentResultEmiCheckPaymentMethod = 'Nguồn tiền';
  static const String paymentResultEmiSupport = 'Liên hệ hỗ trợ';
  static const String paymentResultEmiInterestRate = '{0} %';
  static const String paymentResultEmiToday = 'Hôm nay - {0}';
  static const String emiFeeInfoText = 'Tính vào kỳ sao kê tiếp theo';
  static const String emiMonthlyInstallmentAmount = 'Trả mỗi tháng';
  static const String emiConversionFee = 'Phí chuyển đổi';
  static const String emiMonthlyInterestRate = 'Lãi suất';
  static const String emiAmountDifferenceBetweenDirectAndEmiPayment = 'Chênh lệch với trả thẳng';
  static const String emiOptionUWantEMIPaymentTitle = 'Bạn muốn trả góp tại {0}';
  static const String emiCheckoutUWantEMIPaymentTitle = 'Đăng ký trả góp tại {0}';
  static const String emiOptionSelectTenorTitle = 'trong bao lâu?';
  static const String emiCheckoutSelectedTenorTitle = 'trong {0} tháng';
  static const String emiOptionSeeMoreTenorInfoTitle = 'Xem thêm thông tin';
  static const String emiPaymentSummaryTotalAmount = 'Tổng giá trị đơn hàng';
  static const String outrightPurchaseTitle = 'Bạn đang thanh toán tại {0}';
  static const String emiNotSupportTitle = 'Chưa hỗ trợ trả góp';
  static const String emiGuideToOutrightPaymentDescription =
      'Bạn có thể chọn trả thẳng để tiếp tục thanh toán cho đơn hàng này';
  static const String orderExpiredTitleBottomSheet =
      'Hết thời gian thanh toán, thực hiện lại giao dịch bạn nhé';
  static const String orderExpiredDescriptionBottomSheet =
      'Giao dịch của bạn đã hết thời hạn thanh toán.\nVui lòng thực hiện lại giao dịch để hoàn tất quá trình mua hàng.';
  static const String mwgPromotionSuffixUnSelectedTitle = 'Chọn ngay';
  static const String mwgPromotionSuffixSelectedTitle = 'Thay đổi';
  static const String mwgPaymentSelectPromotion = 'Danh sách ưu đãi';
  static const String mwgPaymentSummaryTotalAmount = 'Tổng giá trị đơn hàng';

  /// EMI confirm and payment
  static const String emiUnqualifiedAfterApplyPromotionTitle = 'Chưa đủ điều kiện trả góp';
  static const String emiUnqualifiedAfterApplyPromotionChangeToOutrightPayment =
      'Chuyển sang trả thẳng';
  static const String emiUnqualifiedAfterApplyPromotionSelectOtherPromotion = 'Chọn ưu đãi khác';
  static const String emiUnqualifiedAfterApplyPromotionDefaultError =
      'Số tiền thanh toán của bạn không đủ điều kiện trả góp.';

  // DOP on WebView
  static const String cancelDOPJourneyTitle = 'Bạn có chắc muốn dừng hành trình mở thẻ không?';
  static const String cancelDOPJourneyDescription =
      '“Tiếp tục” hành trình để không bỏ lỡ các ưu đãi hấp dẫn từ thẻ TPBank EVO';
  static const String webViewDOPJourneyTitle = 'Đăng ký mở thẻ';
  static const String webViewDOPWithErrorCameraPermission =
      'Vui lòng cho phép ứng dụng EVO sử dụng camera để tiếp tục hành trình mở thẻ nhé!';

  // Revamp HomePage for non user
  static const String homepageForNonUserTitle = 'Thẻ tín dụng\nđồng thương hiệu';
  static const String homePageForNonUserDescription = 'Đồng hành cùng\nngân hàng Tiên Phong';
  static const String storyCtaLogin = 'Đăng nhập';
  static const String storyCtaViewDetail = 'Xem chi tiết';
  static const String storyFooterDescription =
      'Điều gì khiến bạn chần chừ? Trò chuyện với \nEVO ngay nhé';

  /// POS limit
  static const String posLimitTitle = 'Thanh toán thất bại do hạn mức quẹt thẻ chưa được cài đặt';
  static const String posLimitDescription =
      'Thao tác 4 bước đơn giản để Cài đặt Hạn mức tại ứng dụng TPBank Mobile và tiếp tục mua sắm nhé!';
  static const String enablePosLimitButton = 'Cách cài đặt';
  static const String enablePosLimitWebTitle = 'Hướng dẫn cài đặt';

  /// Remind POS limit
  static const String remindEnablePosLimitButton = 'Tiếp tục';
  static const String remindPosLimitGuideIntroTitle = 'Bạn đã cài đặt hạn mức thanh toán chưa?';
  static const String remindPosLimitGuideIntroDescription =
      'Cài đặt hạn mức quẹt thẻ/rút tiền theo các bước sau để tiếp tục thanh toán với ứng dụng EVO nhé!';
  static const String remindPosLimitGuideTitle1 = 'Bước 1';
  static const String remindPosLimitGuideDescription1 =
      'Đăng nhập Ứng dụng TPBank Mobile và chọn mục Quản lý thẻ';
  static const String remindPosLimitGuideTitle2 = 'Bước 2';
  static const String remindPosLimitGuideDescription2 =
      'Trên màn hình Quản lý thẻ, chọn mục\nXem thêm';
  static const String remindPosLimitGuideTitle3 = 'Bước 3';
  static const String remindPosLimitGuideDescription3 = 'Chọn Cài đặt hạn mức giao dịch';
  static const String remindPosLimitGuideTitle4 = 'Bước 4';
  static const String remindPosLimitGuideDescription4 = 'Chọn Cài đặt hạn mức quẹt thẻ/rút tiền';
  static const String remindPosLimitGuideTitle5 = 'Bước 5';
  static const String remindPosLimitGuideDescription5 =
      'Cài đặt hạn mức quẹt thẻ/rút tiền với giá trị lớn hơn giá trị đơn hàng';

  /// DOP Native - scan QR code
  static const String dopNativeCameraPermissionDenyTitle = 'Quyền truy cập Camera';
  static const String dopNativeCameraPermissionDenyDescription =
      'Kích hoạt Camera trong cài đặt để quét mã và mở thẻ nhanh chóng';
  static const String dopNativeScanInvalidQrCodeTitle = 'Mã QR chưa đúng';
  static const String dopNativeScanInvalidQrCodeDescription =
      'EVO không thể quét mã QR này, bạn kiểm tra và thử lại nhé';

  /// Tern & condition
  static const String termAndConditionPopupTitle = 'Đồng ý Điều khoản và Điều kiện';
  static const String termAndConditionPopupContent1 = 'Tôi xác nhận đã đọc, hiểu rõ và đồng ý';
  static const String termAndConditionPopupContent2 = 'Điều khoản và Điều kiện';
  static const String termAndConditionPopupContent3 = 'khi sử dụng ứng dụng EVO';
  static const String termAndConditionPopupCTA = 'Tiếp tục';

  /// VNPay QR payment
  static const String orderProcessedByTPB = 'Đơn hàng được xử lý bởi TPBank';

  /// Scan QR Go Evo URL
  static const String scanQRGoEvoUrlWebViewTitle = 'Chi tiết chương trình';

  /// EMI Management
  static const String emiManagementTitle = 'Quản lý trả góp';
  static const String emiManagementEmptyTitle = 'Chưa có giao dịch trả góp';
  static const String emiManagementEmptyDescription =
      'Mọi yêu cầu trả góp qua ứng dụng EVO của bạn hiện ở đây';

  /// EMI Management Detail
  static const String emiManagementDetailTitle = 'Chi tiết trả góp';
  static const String emiManagementTransactionDetailCTA = 'Chi tiết giao dịch';
  static const String emiManagementRemainingAmount = 'Dư nợ trả góp còn lại';
  static const String emiManagementRemainingPeriod = 'Số tháng còn lại';
  static const String emiManagementPaid = 'Đã thanh toán';
  static const String emiManagementDetailMonthTitle = 'tháng';

  /// Activated Card
  static const String activatedCardIntroductionTitle =
      'Bạn cần kích hoạt thẻ và cài đặt hạn mức trước khi thanh toán';
  static const String activatedCardIntroductionDescription =
      'Cài đặt hạn mức để kích hoạt thẻ trước khi thực hiện thanh toán đơn hàng';
  static const String activatedCardIntroductionCTA = 'Tiếp tục';

  /// POS Limit
  static const String posLimitIntroductionTitle = 'Thẻ của bạn chưa cài đặt hạn mức thanh toán';
  static const String posLimitIntroductionDescription =
      'Bạn cần cài đặt hạn mức thanh toán tối đa trên 1 giao dịch để hoàn tất đơn hàng';
  static const String posLimitIntroductionCTA = 'Cài đặt hạn mức';

  /// Activated Card Guidance
  static const String activatedCardGuideTitle =
      'Bạn cần kích hoạt thẻ và cài đặt hạn mức trước khi thanh toán';
  static const String activatedCardGuideDescription =
      'Xem hướng dẫn để kích hoạt thẻ và cài đặt hạn mức trên ứng dụng TPBank';
  static const String activatedCardAcceptedDescription = 'Đã kích hoạt thẻ';

  /// POS Limit Guidance
  static const String posLimitGuideTitle = 'Giá trị đơn hàng đang cao hơn hạn mức thanh toán';
  static const String posLimitGuideDescription =
      'Bạn cần nâng hạn mức thanh toán trên ứng dụng TPBank để hoàn tất được đơn hàng';
  static const String activePosLimitPayAgainCTA = 'Thanh toán lại đơn hàng';
  static const String activePosLimitSetupTpBankCTA = 'Chuyển sang ứng dụng TPBank';
  static const String posLimitAcceptedDescription = 'Đã cài đặt hạn mức POS';

  // Insufficient Credit Limit
  static const String creditLimitInsufficientBottomSheetTitle = 'Hạn mức tín dụng không đủ';
  static const String creditLimitInsufficientBottomSheetDescription =
      'Tách đơn hàng với giá trị nhỏ hơn để thanh toán';

  /// Setup pos limit
  static const String setupPosLimitTitle = 'Cài đặt hạn mức giao dịch';
  static const String posLimitNeedHigherThanOrderAmount =
      'Hạn mức thanh toán cần lớn hơn giá trị đơn hàng';
  static const String setupPosLimitNote =
      'Bạn có thể điều chỉnh lại hạn mức thông qua ứng dụng TPBank Mobile';
  static const String setupPosLimitEnterAmount = 'Nhập số tiền';
  static const String setupPosLimitTooltipContent =
      'Hạn mức tối đa là {0}, bạn có thể điều chỉnh lại trên ứng dụng TPBank Mobile';

  /// Setup POS limit - Waiting popup
  static const String systemNeedTimeToProcess = 'Hệ thống cần thời gian xử lý';
  static const String youWaitAndRetryLater = 'Bạn vui lòng chờ và thử lại sau';
  static const String activatePOSLimitCancellationWarningContent =
      'Nếu rời màn hình này, bạn cần đợi thêm 5 phút để có thể kích hoạt lại';
  static const String activatePOSLimitCancellationWarningTitle = 'Bạn có chắc muốn thoát?';
  static const String activatePOSLimitStayHere = 'Ở lại';
  static const String activatePOSLimitDiscardCancellation = 'Thoát';

  // cashback result popup
  static const String cashBackSuccessTitle = 'Hoàn tiền thành công';
  static const String cashBackSuccessContentPar1 = 'Bạn đã được hoàn ';
  static const String cashBackSuccessContentPar2 = ' từ giao dịch với ';
  static const String viewCashBackCTA = 'Xem tiền hoàn';

  // Maintenance
  static const String maintenanceTitle = 'Hệ thống đang bảo trì';
  static const String systemDuringMaintain = 'Hệ thống đang trong quá trình bảo trì';
  static const String pleaseComebackLater = 'Vui lòng quay lại sau thời gian này';
  static const String from = 'từ';
  static const String day = 'ngày';
  static const String hour = 'giờ';
  static const String minute = 'phút';
  static const String to = 'đến';

  // Rating Review Popup
  static const String cardIssuedCardActivatedRatingReviewPopupTitle =
      'Bạn vừa mở thẻ thành công 🎉';
  static const String cardIssuedCardActivatedRatingReviewPopupContent =
      'Cảm ơn bạn đã tin dùng EVO. Nếu hài lòng, hãy đánh giá 5 sao để EVO phục vụ bạn ngày càng tốt hơn nhé!';
  static const String paymentResultRatingReviewPopupTitle = 'Giao dịch thành công rồi! ✅';
  static const String paymentResultRatingReviewPopupContent =
      'Nếu hài lòng, hãy đánh giá 5 sao cho EVO nhé, mỗi đánh giá của bạn sẽ giúp EVO hoàn thiện trải nghiệm tốt hơn.';
}
